{include file="header.htm" /}

<body class="bodystyle" style="cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <div class="flexigrid">
        <div class="mDiv">
            <div class="ftitle">
                <h3>文件列表</h3>
                <h5>(共{$list|count}条数据)</h5>
            </div>
            <div title="刷新数据" class="pReload"><i class="fa fa-refresh"></i></div>
        </div>
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        <th abbr="" axis="col3" class="">
                            <div class="" style="padding-left:15px ">文件名</div>
                        </th>
                        <th abbr="" axis="col3" class="w200">
                            <div class="tc">文件大小</div>
                        </th>
                        <th abbr="" axis="col6" class="w100">
                            <div class="tc">更新时间</div>
                        </th>
                        <th axis="col1" class="w120">
                            <div class="tc">操作</div>
                        </th>
                       
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%">
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <i class="fa fa-exclamation-circle"></i>没有符合条件的记录
                            </td>
                        </tr>
                    {else/}
                        {foreach name="list" item="vo" key="k" }
                        <tr>
                            <td style="width: 100%">
                                <div style="text-align: left; padding-left: 15px;">
                                  {if condition="in_array($vo['filetype'], array('dir','dir2'))"}
                                  <a href="{:weapp_url('Systemdoctor/Systemdoctor/filemanager_index', array('activepath'=>replace_path($vo.filepath)))}"><img src="__SKIN__/images/{$vo.icon}" border="0" width="16" height="16" align="absmiddle">&nbsp;{$vo.filename}</a>
                                  {else /}
                                    {notempty name="$vo.icon"}
                                    <img src="__SKIN__/images/{$vo.icon}" border="0" width="16" height="16" align="absmiddle" {if condition="!empty($vo.filepath) AND 'image' == $vo.filemine"}onmouseover="layer_tips=layer.tips('<img src=__ROOT_DIR__{$vo.filepath}?v={php}echo time();{/php} class=\'layer_tips_img\'>',this,{tips: [1, '#fff']});"{/if} onmouseout="layer.close(layer_tips);">
                                    {/notempty}
                                    &nbsp;
                                    {eq name="$vo['filemine']" value='image'}
                                        <a href="__ROOT_DIR__{$vo['filepath']}?v={php}echo time();{/php}" target="_blank">{$vo.filename}</a>
                                    {else /}
                                        <a href="{:weapp_url('Systemdoctor/Systemdoctor/filemanager_edit',array('filename'=>$vo['filename'], 'activepath'=>replace_path(dirname($vo['filepath']))))}">{$vo.filename}</a>
                                    {/eq}
                                  {/if}
                                  {$vo.intro}
                                </div>
                            </td>
                            <td class="">
                                <div class="w200 tc">
                                  {$vo.filesize|default=''}
                                </div>
                            </td>
                            <td class="">
                                <div class="w100 tc">
                                  {notempty name="$vo.filetime"}
                                  {$vo.filetime|date='Y-m-d',###}
                                  {/notempty}
                                </div>
                            </td>
                            <td class="">
                                <div class="w120 tc">
                                  {eq name="$vo['filemine']" value='image'}
                                    {if condition="in_array($vo['filetype'], $replaceImgOpArr)"}
                                        <a href="{:weapp_url('Systemdoctor/Systemdoctor/filemanager_replace_img',array('filename'=>$vo['filename'], 'activepath'=>replace_path(dirname($vo['filepath']))))}" class="btn blue"><i class="fa fa-pencil-square-o"></i>替换</a>
                                    {/if}
                                  {else /}
                                    {if condition="in_array($vo['filetype'], $editOpArr)"}
                                        <a href="{:weapp_url('Systemdoctor/Systemdoctor/filemanager_edit',array('filename'=>$vo['filename'], 'activepath'=>replace_path(dirname($vo['filepath']))))}" class="btn blue"><i class="fa fa-pencil-square-o"></i>编辑</a>
                                    {/if}
                                  {/eq}
                                </div>
                            </td>
                        </tr>
                        {/foreach}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        <div class="tDiv">
            <div class="tDiv2">
                <div class="fbutton">
                    <a href="{:weapp_url('Systemdoctor/Systemdoctor/filemanager_newfile', array('activepath'=>replace_path($activepath)))}">
                        <div class="add" title="新建文件">
                            <span class="red"><i class="fa fa-file"></i>新建文件</span>
                        </div>
                    </a>
                </div>
            </div>
            <div style="clear:both"></div>
        </div>
        <!--分页位置-->
    </div>
</div>
<form class="none" id="post_del" method="POST" action="">
  <input type="hidden" name="filename" value="">
  <input type="hidden" name="activepath" value="">
</form>
<script type="text/javascript">
    $(document).ready(function(){

        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });
    });

    function delfilename(obj, formid) {
        layer.confirm('此操作不可恢复，确认删除？', {
                title: false,
                btn: ['确定','取消'] //按钮
            }, function(){
                // 确定
                var form = $('#post_del');
                var filename = $(obj).attr('data-filename');
                var activepath = $(obj).attr('data-activepath');
                var url = $(obj).attr('data-url');
                $(form).find('input[name=filename]').val(filename);
                $(form).find('input[name=activepath]').val(activepath);
                $(form).attr('action', url);
                layer_loading('正在处理');
                $('#post_del').submit();
            }, function(index){
                layer.close(index);
                return false;// 取消
            }
        );
    }
</script>

{include file="footer.htm" /}