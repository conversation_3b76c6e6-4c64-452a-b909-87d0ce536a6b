{include file="header.htm" /}


<body class="bodystyle">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <div class="fixed-bar">
        <div class="item-title"><a class="back" href="{:weapp_url('Systemdoctor/Systemdoctor/filemanager_index')}" title="返回列表"><i class="fa fa-chevron-left"></i></a>
            <div class="subject">
                <h3>模板管理</h3>
                <h5></h5>
            </div>
        </div>
    </div>
    <form class="form-horizontal" id="post_form" action="{:weapp_url('Systemdoctor/Systemdoctor/filemanager_replace_img')}" method="post" enctype="multipart/form-data">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">当前目录</dt>
                <dd class="opt">
                    {$info['activepathname']|default=''}
                    <input type="hidden" name="activepath" value="{$info['activepath']|default=''}">
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">图片名</dt>
                <dd class="opt">
                    {$info['filename']|default=''}
                    <input type="hidden" name="filename" value="{$info.filename|default=''}">
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="url">上传图片</label>
                </dt>
                <dd class="opt">
                    <input type="file" name="upfile" value="" class="input-txt">
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <div class="bot">
                <a href="JavaScript:void(0);" onclick="checkForm();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    // 判断输入框是否为空
    function checkForm(){
        var isempty = true;
        $.each($('input[name^=upfile]'), function(index, item){
            if ($.trim($(item).val()) != '' && $(item).val() != undefined && $(item).val() != null) {
                isempty = false;
            }
        })
        if(isempty == true){
            showErrorMsg('请选择上传图片！');
            return false;
        }
        layer_loading('正在处理');
        $('#post_form').submit();
    }
</script>
{include file="footer.htm" /}
