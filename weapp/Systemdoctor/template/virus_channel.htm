<!doctype html>
<html>
<head>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8">
    <link rel="stylesheet" type="text/css" href="__PUBLIC__/plugins/bootstrap/css/bootstrap.min.css?v={$version}">
    <script type="text/javascript">
        var __root_dir__ = "__ROOT_DIR__";
        var __lang__ = "{$admin_lang}";
    </script>
    <script type="text/javascript" src="__SKIN__/js/jquery.js"></script>
    <script type="text/javascript" src="__PUBLIC__/plugins/layer-v3.1.0/layer.js"></script>
</head>
<style>
    .gray-bg {
        background-color: #f3f3f4;
    }
    .wrapper-content {
        padding: 20px;
    }
    .ibox {
        clear: both;
        margin-bottom: 25px;
        margin-top: 0;
        padding: 0;
    }
    .ibox-content {
        clear: both;
        background-color: #ffffff;
        color: inherit;
        padding: 15px 20px 20px 20px;
        border-color: #e7eaec;
        -webkit-border-image: none;
        -o-border-image: none;
        border-image: none;
        border-style: solid solid none;
        border-width: 1px 0px;
    }
    .progress {
        height: 20px;
        margin-bottom: 20px;
        overflow: hidden;
        background-color: #f5f5f5;
        border-radius: 4px;
        -webkit-box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
        box-shadow: inset 0 1px 2px rgba(0,0,0,.1);
    }
    .progress-bar{
        height: 100%;
    }
    .progress-bar-success {
        background-color: #1c84c6;
    }
    .ncap-btn-green {
        margin: 20px auto 0;
        width: 100px;
        height: 36px;
        line-height: 36px;
        background-color: #4fc0e8;
        border:1px solid #3aa8cf;
        text-align: center;
        vertical-align: middle;
        display: block;
        padding: 0px 9px;
        border-radius: 3px;
        cursor: pointer;
        color: #fff;
        font-size: 13px;
    }
    a.ncap-btn-green:hover{
        background-color:#3ab0da;
        color: #fff;
        text-decoration: none;
    }
</style>
<body class="gray-bg">
<div class="ibox float-e-margins">

    <div class="ibox-content">
        <div class="progress progress-striped active">
            <div class="progress-bar progress-bar-success" role="progressbar"
                 aria-valuenow="60" aria-valuemin="0" aria-valuemax="100" style="width:0%">
            </div>
        </div>
        <div style="text-align:center" class="success">
            <div class="finash" style="">
                <span>
                    <font id="title">正在生成</font>
                    <span class="filetypename"></span>
                    <span class="filetypeid"></span>，
                </span>
                <span class="filefindex"></span>
                已完成<span class="percent" style="color:#ff0000 ;font-size:14px;">0</span>%
            </div>
        </div>
    </div>
    <a href="javascript:void(0);" id="a_close_pop" onclick="parent.layer.closeAll();" class="ncap-btn-green" style="display: none;">关闭本窗口</a>
    <div class="ibox-content" id="error_msg" style="display: none">
    </div>
</div>

<script type="text/javascript">
    $('#a_close_pop').hide();
    var achieve = 0;
    var allpagetotal = 0;
    var msg = "";
    var timestamp1 = Date.parse(new Date());
    function send(){
        $.ajax({
            url:"{:weapp_url('Systemdoctor/Systemdoctor/buildChannel')}",
            type:'POST',
            dataType:'json',
            data:{'achieve':achieve,_ajax:1},
            timeout : 360000, //超时时间设置，单位毫秒 设置了 1小时
            beforeSend:function(){
                if (achieve == 0) {
                    $("#title").html('正在扫描文件');
                    $(".progress-bar").css('width','0.5%');
                    $(".percent").text('0.5');
                }
            },
            success:function(res){
                achieve = res.data.achieve;
                allpagetotal = res.data.allpagetotal;
                msg = res.msg;
                if (msg !== ""){
                    parent.$("#virus_files").append(msg);
                }
                progress = ((achieve/allpagetotal)*100).toFixed(1);
                $(".progress-bar").css('width',progress+'%');
                $(".percent").text(progress);
                if (achieve === allpagetotal){
                    var timestamp2 = Date.parse(new Date());
                    var timestamp3 = (timestamp2 - timestamp1) / 1000;
                    if (timestamp3 < 1) timestamp3 = 1;
                    $(".finash").remove();
                    $(".progress-bar").css('width','100%');
                    $(".progress").removeClass('progress-striped ').removeClass('active');
                    $(".success").html("扫描完成，共耗时：<font color='red'>"+timestamp3+"</font> 秒");
                    $('#a_close_pop').show();
//                    parent.layer.closeAll();
                    return false;
                }
                send();
            },
            error:function(e){
                parent.layer.alert(e.responseText, {icon: 5, title:false});
            }
        });
    }
    send();
</script>
</body>
</html>



