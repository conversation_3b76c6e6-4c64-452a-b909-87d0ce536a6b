{include file="header.htm" /}
<body style="background-color: rgb(255, 255, 255); overflow: auto; cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <div class="flexigrid">
        <div class="mDiv">
            <div class="ftitle">
                <h3>日志列表</h3>
                <h5>(共{$pageObj->totalRows}条记录)</h5>
            </div>
            <div title="刷新数据" class="pReload"><i class="fa fa-refresh"></i></div>
            <form class="navbar-form form-inline" action="{:weapp_url('Systemdoctor/Systemdoctor/admin_log')}" method="get" onsubmit="layer_loading('正在处理');">
                {$searchform.hidden|default=''}
                <div class="sDiv">
                    <div class="sDiv2">
                        <input type="text" size="30" name="keywords" class="qsbox" placeholder="搜索相关数据...">
                        <input type="submit" class="btn" value="搜索">
                    </div>
                    <div class="sDiv2">
                        <input type="button" class="btn" value="重置" onClick="window.location.href='{:weapp_url('Systemdoctor/Systemdoctor/admin_log')}';">
                    </div>
                </div>
            </form>
        </div>
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        <th class="sign w40" axis="col0">
                            <div class="tc">选择</div>
                        </th>
                        <th abbr="log_id" axis="col5" class="w40">
                            <div class="tc">ID</div>
                        </th>
                        <th abbr="admin_id" axis="col3" class="w250">
                            <div class="tc">操作人</div>
                        </th>
                        <th abbr="log_info" axis="col4">
                            <div class="">操作信息</div>
                        </th>
                        <th abbr="log_ip" axis="col6" class="w100">
                            <div class="tc">IP</div>
                        </th>
                        <th abbr="log_time" axis="col6" class="w160">
                            <div class="tc">操作时间</div>
                        </th>
                        <th axis="col1" class="w120">
                            <div class="tc">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%">
                    <tbody>
                    {empty name="list"}
                    <tr>
                        <td class="no-data" align="center" axis="col0" colspan="50">
                            <i class="fa fa-exclamation-circle"></i>没有符合条件的记录
                        </td>
                    </tr>
                    {else/}
                    {volist name="list" id="vo"}
                    <tr>
                        <td class="sign">
                            <div class="w40 tc"><input type="checkbox" name="ids[]" value="{$vo.log_id}"></div>
                        </td>
                        <td class="sort">
                            <div class="w40 tc">
                                {$vo.log_id}
                            </div>
                        </td>
                        <td class="">
                            <div class="w250 tc">
                                {eq name='$vo.admin_id' value='-1'}
                                    游客
                                {else /}
                                    {$vo.user_name|default='———'}
                                {/eq}
                            </div>
                        </td>
                        <td style="width: 100%">
                            <div style="">
                                {$vo.log_info|htmlspecialchars_decode=###}
                            </div>
                        </td>
                        <td class="">
                            <div class="w100 tc">
                                {$vo.log_ip}
                            </div>
                        </td>
                        <td class="">
                            <div class="w160 tc">
                                {$vo.log_time|date='Y-m-d H:i:s',###}
                            </div>
                        </td>
                        <td>
                            <div class="w120 tc">
                                <a class="btn red"  href="javascript:void(0)" data-url="{:weapp_url('Systemdoctor/Systemdoctor/del_admin_log')}" data-id="{$vo.log_id}" onClick="delfun(this);"><i class="fa fa-trash-o"></i>删除</a>
                            </div>
                        </td>
                    </tr>
                    {/volist}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        <div class="tDiv">
            <div class="tDiv2">
                <div class="fbutton checkboxall">
                    <input type="checkbox" onclick="javascript:$('input[name*=ids]').prop('checked',this.checked);">
                </div>
                <div class="fbutton">
                    <a onclick="batch_del(this, 'ids');" data-url="{:weapp_url('Systemdoctor/Systemdoctor/del_admin_log')}">
                        <div class="add" title="批量删除">
                            <span><i class="fa fa-close"></i>批量删除</span>
                        </div>
                    </a>
                </div>
            </div>
            <div style="clear:both"></div>
        </div>
        <!--分页位置-->
        {$pageStr}
    </div>
</div>
<script>
    $(document).ready(function(){
        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });
    });
</script>
{include file="footer.htm" /}
