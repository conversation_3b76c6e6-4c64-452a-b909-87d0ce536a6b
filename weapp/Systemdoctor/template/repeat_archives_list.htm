{include file="header.htm" /}
<body style="background-color: rgb(255, 255, 255); overflow: auto; cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    <div class="flexigrid">
        <div class="mDiv">
            <div class="ftitle">
                <h3>重复文档列表</h3>
                <h5>(共{$pageObj->totalRows}条记录)</h5>
            </div>
            <div title="刷新数据" class="pReload"><i class="fa fa-refresh"></i></div>
            <form class="navbar-form form-inline" action="{:weapp_url('Systemdoctor/Systemdoctor/repeat_archives_list')}" method="get" onsubmit="layer_loading('正在处理');">
                {$searchform.hidden|default=''}
                <div class="sDiv">
                    <div class="sDiv2">
                        <input type="text" size="30" name="keywords" class="qsbox" value="{$Think.Request.keywords}" placeholder="标题搜索...">
                        <input type="submit" class="btn" value="搜索">
                    </div>
                    <div class="sDiv2">
                        <input type="button" class="btn" value="重置" onClick="window.location.href='{:weapp_url('Systemdoctor/Systemdoctor/repeat_archives_list')}';">
                    </div>
                </div>
            </form>
        </div>
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        <th class="sign w40" axis="col0">
                            <div class="tc">选择</div>
                        </th>
                        <th abbr="article_show" axis="col5" class="w50">
                            <div class="tc">ID</div>
                        </th>
                        <th abbr="ac_id" axis="col4">
                            <div class="">标题</div>
                        </th>
                        <th abbr="article_title" axis="col3" class="w160">
                            <div class="">所属栏目</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w100">
                            <div class="tc">发布时间</div>
                        </th>
                        <th axis="col1" class="w120">
                            <div class="tc">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%">
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <i class="fa fa-exclamation-circle"></i>没有符合条件的记录
                            </td>
                        </tr>
                    {else/}
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="sign">
                                <div class="w40 tc"><input type="checkbox" name="ids[]" value="{$vo.aid}"></div>
                            </td>
                            <td class="sort">
                                <div class="w50 tc">
                                    {$vo.aid}
                                </div>
                            </td>
                            <td style="width: 100%">
                                <div style="">
                                    <a href="{$vo.arcurl}" target="_blank">{$vo.title}</a>
                                </div>
                            </td>
                            <td class="">
                                <div class="w160">
                                    {$vo.typename}
                                </div>
                            </td>
                            <td class="">
                                <div class="w100 tc">
                                    {$vo.add_time|MyDate='Y-m-d',###}
                                </div>
                            </td>
                            <td>
                                <div class="w120 tc">
                                    <a class="btn red"  href="javascript:void(0);" data-url="{:url('Article/del')}" data-id="{$vo.aid}" data-deltype="pseudo" onClick="delfun(this);"><i class="fa fa-trash-o"></i>删除</a>
                                    <a href="{$vo.arcurl}" target="_blank" class="btn blue"><i class="fa fa-pencil-square-o"></i>浏览</a>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        <div class="tDiv">
            <div class="tDiv2">
                <div class="fbutton checkboxall">
                    <input type="checkbox" onclick="javascript:$('input[name*=ids]').prop('checked',this.checked);">
                </div>
                <div class="fbutton">
                    <a onclick="batch_del(this, 'ids');" data-url="{:url('Archives/del')}">
                        <div class="add" title="批量删除">
                            <span><i class="fa fa-close"></i>批量删除</span>
                        </div>
                    </a>
                </div>
            </div>
            <div style="clear:both"></div>
        </div>
        <!--分页位置-->
        {$pageStr}
    </div>
</div>
<script>
    $(document).ready(function(){
        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });
    });
</script>
{include file="footer.htm" /}