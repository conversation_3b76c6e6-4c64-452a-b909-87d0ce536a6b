{include file="header.htm" /}
<body style="background-color: #FFF; overflow: auto;">
<div id="toolTipLayer" style="position: absolute; z-index: 9999; display: none; visibility: visible; left: 95px; top: 573px;"></div>
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    {include file="bar.htm" /}
    <form class="form-horizontal" id="post_form" action="" method="post">
        <div class="ncap-form-default">
            <dl class="row">
                <dt class="tit">
                    <label for="adminlogin_is_on">是否启用</label>
                </dt>
                <dd class="opt">
                    <div class="onoff">
                        <label for="adminlogin_is_on1" class="cb-enable {if condition='!empty($row.is_on)'}selected{/if}">启用</label>
                        <label for="adminlogin_is_on0" class="cb-disable {if condition='empty($row.is_on)'}selected{/if}">关闭</label>
                        <input id="adminlogin_is_on1" name="captcha[admin_login][is_on]" value="1" type="radio" {if condition='!empty($row.is_on)'}checked="checked"{/if}>
                        <input id="adminlogin_is_on0" name="captcha[admin_login][is_on]" value="0" type="radio" {if condition='empty($row.is_on)'}checked="checked"{/if}>
                    </div>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>            
            <dl class="row">
                <dt class="tit">
                    <label for="adminlogin_codeSet">预置字符</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="captcha[admin_login][config][codeSet]" placeholder="验证码预置字符" value="{$row.config.codeSet|default=$row.default.codeSet}" id="codeSet" class="input-txt">
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="adminlogin_fontSize">字号大小</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="captcha[admin_login][config][fontSize]" placeholder="验证码字体大小(px)" value="{$row.config.fontSize|default=$row.default.fontSize}" id="fontSize" class="input-txt">
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="adminlogin_useCurve">混淆曲线</label>
                </dt>
                <dd class="opt">
                    <div class="onoff">
                        <label for="adminlogin_useCurve1" class="cb-enable {if condition="$row.config.useCurve == '1'"}selected{/if}">开</label>
                        <label for="adminlogin_useCurve0" class="cb-disable {if condition="empty($row.config.useCurve)"}selected{/if}">关</label>
                        <input id="adminlogin_useCurve1" name="captcha[admin_login][config][useCurve]" value="1" type="radio" {if condition="$row.config.useCurve == '1'"}checked="checked"{/if}>
                        <input id="adminlogin_useCurve0" name="captcha[admin_login][config][useCurve]" value="0" type="radio" {if condition="empty($row.config.useCurve)"}checked="checked"{/if}>
                    </div>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="adminlogin_useNoise">添加杂点</label>
                </dt>
                <dd class="opt">
                    <div class="onoff">
                        <label for="adminlogin_useNoise1" class="cb-enable {if condition="$row.config.useNoise == '1'"}selected{/if}">是</label>
                        <label for="adminlogin_useNoise0" class="cb-disable {if condition="empty($row.config.useNoise)"}selected{/if}">否</label>
                        <input id="adminlogin_useNoise1" name="captcha[admin_login][config][useNoise]" value="1" type="radio" {if condition="$row.config.useNoise == '1'"}checked="checked"{/if}>
                        <input id="adminlogin_useNoise0" name="captcha[admin_login][config][useNoise]" value="0" type="radio" {if condition="empty($row.config.useNoise)"}checked="checked"{/if}>
                    </div>
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <dl class="row">
                <dt class="tit">
                    <label for="adminlogin_length">显示位数</label>
                </dt>
                <dd class="opt">
                    <input type="text" name="captcha[admin_login][config][length]" placeholder="验证码图片高度" value="{$row.config.length|default=$row.default.length}" id="length" class="input-txt" onkeyup="this.value=this.value.replace(/[^0-9]/g,'');" onbeforepaste="clipboardData.setData('text',clipboardData.getData('text').replace(/\[^0-9]/g,''));">
                    <span class="err"></span>
                    <p class="notic"></p>
                </dd>
            </dl>
            <div class="bot">
                <input type="hidden" name="inc_type" id="inc_type" value="{$inc_type|default='default'}">
                <a href="JavaScript:void(0);" onclick="checkForm();" class="ncap-btn-big ncap-btn-green" id="submitBtn">确认提交</a>
            </div>
        </div>
    </form>
</div>
<script type="text/javascript">
    // 判断输入框是否为空
    function checkForm(){
        if($('#codeSet').val() == ''){
            showErrorMsg('预置字符不能为空');
            $('#codeSet').focus();
            return false;
        }
        if($('#fontSize').val() == '' || 0 == $('#fontSize').val()){
            showErrorMsg('字体大小必须大于0');
            $('#fontSize').focus();
            return false;
        }
        if($('#length').val() == '' || 0 == $('#length').val()){
            showErrorMsg('显示位数必须大于0');
            $('#length').focus();
            return false;
        }

        layer_loading('正在处理');
        $('#post_form').submit();
    }
    
</script>
{include file="footer.htm" /}