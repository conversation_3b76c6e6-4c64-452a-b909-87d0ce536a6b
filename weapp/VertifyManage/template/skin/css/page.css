.pagination {
    border-radius: 4px;
    display: inline-block;
    margin: 20px 0;
    padding-left: 0px;
}
.pagination > li {
    display: inline;
}
.pagination > li > a, .pagination > li > span {
    background-color: #fff;
    border: 1px solid #ddd;
    color:#666;
    float: left;
    line-height: 1.42857;
    margin-left: -1px;
    padding: 6px 12px;
    position: relative;
    text-decoration: none;
}
.pagination > li:first-child > a, .pagination > li:first-child > span {
    border-bottom-left-radius: 4px;
    border-top-left-radius: 4px;
    margin-left: 0;
}
.pagination > li:last-child > a, .pagination > li:last-child > span {
    border-bottom-right-radius: 4px;
    border-top-right-radius: 4px;
}
.pagination > li > a:focus, .pagination > li > a:hover, .pagination > li > span:focus, .pagination > li > span:hover {
    background-color: #eee;
    border-color: #ddd;
    color: #23527c;
}
.pagination > .active > a, .pagination > .active > a:focus, .pagination > .active > a:hover, .pagination > .active > span, .pagination > .active > span:focus, .pagination > .active > span:hover {
    background-color:#41BEDD;
    border-color: #41BEDD;
    color: #fff;
    cursor: default;
    z-index: 2;
}
.pagination > .disabled > a, .pagination > .disabled > a:focus, .pagination > .disabled > a:hover, .pagination > .disabled > span, .pagination > .disabled > span:focus, .pagination > .disabled > span:hover {
    background-color: #fff;
    border-color: #ddd;
    color: #555;
    cursor: pointer;
}
.pagination-lg > li > a, .pagination-lg > li > span {
    font-size: 18px;
    padding: 10px 16px;
}
.pagination-lg > li:first-child > a, .pagination-lg > li:first-child > span {
    border-bottom-left-radius: 6px;
    border-top-left-radius: 6px;
}
.pagination-lg > li:last-child > a, .pagination-lg > li:last-child > span {
    border-bottom-right-radius: 6px;
    border-top-right-radius: 6px;
}
.pagination-sm > li > a, .pagination-sm > li > span {
    font-size: 12px;
    padding: 5px 10px;
}
.pagination-sm > li:first-child > a, .pagination-sm > li:first-child > span {
    border-bottom-left-radius: 3px;
    border-top-left-radius: 3px;
}
.pagination-sm > li:last-child > a, .pagination-sm > li:last-child > span {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
}
.pager {
    list-style: outside none none;
    margin: 20px 0;
    padding-left: 0;
    text-align: center;
}
.pager li {
    display: inline;
}
.pager li > a, .pager li > span {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 15px;
    display: inline-block;
    padding: 5px 14px;
}
.pager li > a:focus, .pager li > a:hover {
    background-color: #eee;
    text-decoration: none;
}
.pager .next > a, .pager .next > span {
    float: right;
}
.pager .previous > a, .pager .previous > span {
    float: left;
}
.pager .disabled > a, .pager .disabled > a:focus, .pager .disabled > a:hover, .pager .disabled > span {
    background-color: #fff;
    color: #777;
    cursor: not-allowed;
}      
.dataTables_paginate{text-align: center;}