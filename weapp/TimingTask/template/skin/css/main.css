@charset "utf-8";
* { word-wrap: break-word; outline: none; }
body { background: #FFF; min-width: 1000px; }
html, body { height: 100%; overflow: hidden; }
html { -webkit-text-size-adjust: none; }
body, td, input, textarea, select,
button { color: #555; font-size: 13px; font-family: "Microsoft Yahei", "Lucida Grande", Verdana, Lucida, Helvetica, Arial, sans-serif; }
body, ul, ol, li, dl, dt, dd, p, h1, h2, h3, h4, h5, h6, form, fieldset { margin: 0; padding: 0; }
ul, ol, li { list-style-image: none; list-style-type: none; }
h1, h2, h3, h4, h5, h6 { font-size: 12px; }
a { color: #3b639f; text-decoration: none; blr:expression(this.onFocus=this.blur()) }
a:hover { color: #CD0200; text-decoration: none; }
a img { border: none; }
em, cite, th { font-style: normal; font-weight: normal; }
table { border-collapse: collapse; }
th { text-align: left; }
input, button, select, textarea { outline: none; margin: 0px; padding:0px;}
i.fa { font-size: 14px; vertical-align: baseline; margin-right: 4px; }/*字体图标属性*/
.tl { text-align: left; }
.tc { text-align: center; }
.tr { text-align: right; }
.nobg { background-color: transparent !important; background-image: none !important; }
.nopd { padding: 0!important; }
.nobd { border-width: 0!important; border-color: transparent!important; border-style: none!important; border-radius: 0!important; }
.nobs, .nobs:hover { box-shadow: none!important; }
.none {display: none;}
.blocki{ display:inline-block !important; }
i,em{ font-style: normal; }
/* ==========================
 * 为旧版本浏览器格式化Html5元素
 * ========================== */
article, aside, dialog, footer, header, section, footer, nav, figure, menu { display: block; }

/*长度高度
******************************/
.w10 { width: 10px; }
.w20 { width: 20px; }
.w30 { width: 30px; }
.w40 { width: 40px !important; }
.w50 { width: 50px !important; }
.w60 { width: 60px !important; }
.w65 { width: 65px !important; }
.w70 { width: 70px !important; }
.w80 { width: 80px !important; }
.w90 { width: 90px; }
.w100 { width: 100px !important; }
.w110 { width: 110px !important; }
.w120 { width: 120px !important; }
.w130 { width: 130px !important; }
.w150 { width: 150px !important; }
.w160 { width: 160px; }
.w180 { width: 180px; }
.w200 { width: 200px !important; }
.w210 { width: 210px !important; }
.w230 { width: 230px; }
.w240 { width: 240px; }
.w250 { width: 250px !important; }
.w270 { width: 270px; }
.w300 { width: 300px !important; }
.w350 { width: 350px; }
.w340 { width: 340px; }
.w400 { width: 400px!important; }
.w450 { width: 450px!important; }
.w500 { width: 500px; }
.w600 { width: 600px !important; }
.w700 { width: 700px; }
.w780 { width: 780px; }
.w800 { width: 800px; }
/*边距
******************************/
.m0 { margin: 0!important; }
.m10 { margin: 10px; }
.m15 { margin: 15px !important; }
.m30 { margin: 30px; }
.mt5 { margin-top: 5px; }
.mt10 { margin-top: 10px !important; }
.mt15 { margin-top: 15px; }
.mt20 { margin-top: 20px !important; }
.mt30 { margin-top: 30px !important; }
.mt50 { margin-top: 50px !important; }
.mt100 { margin-top: 100px; }
.mb5 { margin-bottom: 5px !important; }
.mb10 { margin-bottom: 10px !important; }
.mb15 { margin-bottom: 15px; }
.mb20 { margin-bottom: 20px; }
.mb30 { margin-bottom: 30px !important; }
.mb50 { margin-bottom: 50px; }
.mb100 { margin-bottom: 100px; }
.ml5 { margin-left: 5px!important; }
.ml10 { margin-left: 10px!important; }
.ml15 { margin-left: 15px; }
.ml20 { margin-left: 20px; }
.ml30 { margin-left: 30px; }
.ml50 { margin-left: 50px; }
.ml100 { margin-left: 100px !important; }
.ml200 { margin-left: 200px !important; }
.mr5 { margin-right: 5px !important; }
.mr10 { margin-right: 10px !important; }
.mr15 { margin-right: 15px !important; }
.mr20 { margin-right: 20px; }
.mr30 { margin-right: 30px !important; }
.mr50 { margin-right: 50px !important; }
.mr100 { margin-right: 100px; }
/*边距
******************************/
.p10 { padding: 10px; }
.p15 { padding: 15px; }
.p30 { padding: 30px; }
.pt5 { padding-top: 5px; }
.pt10 { padding-top: 10px; }
.pt15 { padding-top: 15px; }
.pt20 { padding-top: 20px; }
.pt30 { padding-top: 30px; }
.pt50 { padding-top: 50px; }
.pt100 { padding-top: 100px; }
.pb5 { padding-bottom: 5px; }
.pb10 { padding-bottom: 10px; }
.pb15 { padding-bottom: 15px; }
.pb20 { padding-bottom: 20px !important; }
.pb30 { padding-bottom: 30px; }
.pb50 { padding-bottom: 50px; }
.pb100 { padding-bottom: 100px; }
.pl5 { padding-left: 5px; }
.pl10 { padding-left: 10px; }
.pl15 { padding-left: 15px; }
.pl20 { padding-left: 20px; }
.pl30 { padding-left: 30px; }
.pl50 { padding-left: 50px; }
.pl100 { padding-left: 100px; }
.pr5 { padding-right: 5px; }
.pr10 { padding-right: 10px; }
.pr15 { padding-right: 15px; }
.pr20 { padding-right: 20px; }
.pr30 { padding-right: 30px; }
.pr50 { padding-right: 50px; }
.pr100 { padding-right: 100px; }
.fl{ float: left; }
.fr{ float: right; }
/* 返回顶部、底部控制 */
#goTop { background-color: #FFF; border-radius: 4px; width: 32px; height: 64px; border: solid 1px #E6E6E6; position: fixed; z-index: 99; bottom: -166px; right: 10px; overflow: hidden; opacity: 0.5; box-shadow: 0 0 10px 0 rgba(153,153,153,0.25); }
#goTop:hover { opacity: 1; box-shadow: 0 0 0 2px rgba(153,153,153,0.075); }
#goTop a { color: #CCC; text-align: center; display: block; width: 32px; height: 32px; margin-top: -1px; border-top: dashed 1px #E6E6E6; cursor: pointer; }
#goTop a i { font-size: 28px; line-height: 32px; margin: 0; }
#goTop a:hover { color: #1bbc9d; }
/*debug*/
.trace { font-family: Verdana, Geneva, sans-serif; font-size: 12px; background: #FAFAFA; margin: 6px; border: 1px dashed #E7E7E7; padding: 8px; }
.trace fieldset { margin: 5px; }
.trace fieldset legend { color: #333; font-weight: bold }
.trace fieldset div { line-height: 16px; padding: 10px; overflow: auto; height: 300px; text-align: left; }
.nc-row { font-size: 0!important; *word-spacing:-1px/*IE6、7*/;}
.nc-row li { font-size: 12px; vertical-align: top; letter-spacing: normal; display: inline-block!important; *display: inline/*IE7*/; *zoom:1/*IE7*/;}
.clear { font-size: 0; line-height: 0; height: 0; clear: both; float: none; padding: 0; margin: 0; border: 0; zoom: 1; }

/* ==========================
 * 定义部分jquery ui样式
 * ========================== */
/*title文字提示*/
.ui-tooltip { color: #FFF !important; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#E5333333', endColorstr='#E5333333')!important;
background: rgba(51,51,51,0.9) !important; border: 0!important; box-shadow: 0 0 5px rgba(0,0,0,0.25)!important; }
/*用于picTip.js配合图片显示
******************************/
.trans_msg { background-color: #FFF; padding: 4px; border: solid 1px #CCC; box-shadow: 0 0 0 2px rgba(153,153,153,0.1); filter: alpha(opacity=100, enabled=1) revealTrans(duration=.2, transition=1) blendtrans(duration=.2); }
.trans_msg img { display: block; max-width: 150px; max-height: 150px; }
.warp-all { width: 1000px; margin: 0 auto; }
/*dialog弹出框*/
.dialog_wrapper { padding: 0 !important; border-radius: 0 !important; box-shadow: 0 0 10px rgba(0,0,0,0.25) !important; }
.dialog_body { border: solid 1px #C8C8C8!important; }
.dialog_head { border-bottom: none!important; }
.dialog_title { height: 24px!important; }
.dialog_title_icon { font-size: 16px!important; font-weight: normal!important; line-height: 24px!important; color: #333!important; }
.dialog_close_button { font-size: 12px!important; line-height: 20px!important; color: #999!important; background-color: #F5F5F5!important; text-align: center!important; width: 20px!important; height: 20px!important; border: none!important; border-radius: 50%!important; position: absolute!important; right: 10px!important; top: 8px!important; }
.dialog_close_button:hover { color: #FFF!important; background-color: #F33!important; }
.highcharts-container { margin-top: 20px !important; border: solid 1px #D7D7D7!important; box-shadow: 0 0 0 2px rgba(204,204,204,0.25); }
.highcharts-container .highcharts-title { display: block!important; width: 100% !important; }
.highcharts-container .highcharts-title tspan { font-size: 16px !important; font-weight: normal !important; position: absolute!important; z-index: 1!important; top: 0!important; left: 0!important; }
/*表单元素样式*/
input[type="text"],
input[type="password"],
textarea,
select,
.editable,
.editable-tarea { color: #555555; background-color : #FFF; border: solid 1px #ccc; transition: border linear .2s, box-shadow linear .2s; -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;
    transition: border-color ease-in-out 0.15s, box-shadow ease-in-out 0.15s;}
input[type="text"],
input[type="password"],
textarea,
select,
.editable,
.editable2,
.editable-tarea,
.editable-tarea2 { padding: 4px 6px; /*Firefox\Chrome\Safari\IE9\元素圆角效果*/ resize: none;/*禁止调节元素的尺寸*/ }
input[type="text"]:focus,
input[type="text"]:hover,
input[type="text"]:active,
input[type="password"]:focus,
input[type="password"]:hover,
input[type="password"]:active,
textarea:hover,
textarea:focus,
textarea:active { color: #33464F; background-color: #fff; border: 1px solid; border-color:rgba(82,168,236,0.8); -moz-box-shadow: 0 0 0 2px rgba(82, 168, 236, 0.15); -webkit-box-shadow: 0 0 0 0 2px rgba(82, 168, 236, 0.15); box-shadow: 0 0 0 2px rgba(82, 168, 236, 0.15); outline: 0 none; }
input[disabled="disabled"],
input[readonly="readonly"],
input[disabled="disabled"]:hover,
input[readonly="readonly"]:hover,
input[disabled="disabled"]:focus,
input[readonly="readonly"]:focus { background: #F5F5F5; border-color: #D7D7D7; box-shadow: none; }
.editable2,
.editable-tarea2 { color: #33464F; background-color: #FFF; border: 1px dotted; border-color: #71CBEF; }
textarea { resize: vertical !important;/*Textarea支持调节元素的高度*/ }
.editable,
.editable2,
input[type="text"],
input[type="password"] { line-height: 20px; white-space: nowrap; display: inline-block; height: 20px; overflow: hidden; cursor: text; font-size: 13px }
.editable-tarea,
.editable-tarea2,
textarea { line-height: 18px; display: inline-block; height: 36px; cursor: text; overflow: auto; }
.tarea { height: 75px; width: 400px; }
.txt,
select,
.vmiddle { vertical-align: middle; }
.sort input,
.sort .editable,
.sort .editable2 { width: 36px; margin-top: -5px; text-align: center;}
.name input,
.name .editable,
.name .editable2 { width: 250px; }
.tag input,
.tag .editable,
.tag .editable2 { width: 480px; }
.goods-name textarea,
.editable-tarea,
.editable-tarea2 { width: 250px; }
.class input,
.class .editable,
.class .editable2 { width: 120px; }
input.readonly,
textarea.readonly,
textarea.readonly:focus,
textarea.readonly:hover,
input.readonly:focus,
input.readonly:hover { backgorund: #FFF; border: solid 1px; border-color: #EEE #F5F5F5 #F5F5F5 #EEE; }
.checkbox { display: inline-block; vertical-align: middle; margin-right: 4px; zoom: 1; }
.input-file-show { background-color: #FFF; vertical-align:middle; display: inline-block; padding-left: 26px; border: solid 1px #D7D7D7; border-radius: 4px; position: relative; z-index: 1; }
.input-file-show:hover { border-color: #00B7EE; -moz-box-shadow: 0 0 5px rgba(44, 188, 163, 0.5); -webkit-box-shadow: 0 0 5px rgba(44, 188, 163, 0.5); box-shadow: 0 0 5px rgba(44, 188, 163, 0.5); }
.input-file-show span.show { width: 22px; height: 24px; text-align: center; padding: 2px; position: absolute; z-index: 2; top: 0; left: 0; }
.input-file-show span.show a { color: #AAA; display: block; width: 22px; height: 22px; }
.input-file-show:hover span.show a,
.input-file-show span.show a:hover { color: #00B7EE; text-decoration: none; }
.input-file-show span.show i { font-size: 14px; line-height: 24px; display: block; }
.input-file-show span.show img { max-width: 22px; max-height: 24px; }
.type-file-box { display: block; width: 360px; height: 28px; position: relative; z-index: 1; }
.type-file-text { line-height: 26px !important; display: block; width: 261px; height: 26px !important; float: left !important; padding: 0 !important; margin: 0 !important; border: none 0 !important; border-radius: 0 !important; }
.type-file-button,
.type-file-button:focus { background-color: #E6E6E6; display: block; width: 99px; height: 28px; float: left !important; border: 0; border-radius: 0 3px 3px 0; }
.input-file-show:hover .type-file-button { color: #FFF; background-color: #00B7EE; }
.type-file-file { width: 362px; height: 28px; position: absolute; top: 0; right: 0; filter:alpha(opacity:0);
opacity: 0; cursor: pointer; }
.type-file-preview { background: #FFF; display: none; padding: 5px; border: solid 5px #71CBEF; position: absolute; z-index: 999; }
.image_display .type-file-show { width: 16px; height: 16px; padding: 2px; border: solid 1px #D8D8D8; cursor: auto; }
/*按钮*/
a.ncap-btn-mini { font: normal 12px/20px arial; color: #777; background-color: #F5F5F5; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 0 10px; margin-right: 2px; border: solid 1px #DCDCDC; cursor: pointer; }
a.ncap-btn-dis { font: normal 12px/20px "microsoft yahei"; text-decoration: none; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 2px 9px; border: solid 1px; border-radius: 3px;background-color:#c5c5c5;color:rgb(119, 119, 119);border-color:#c5c5c5; cursor: auto;text-decoration: none;}
a:hover.ncap-btn-mini { text-decoration: none; color: #333; background-color: #E6E6E6; border-color: #CFCFCF #CFCFCF #B3B3B3 #CFCFCF; }
a.ncap-btn { font: normal 12px/20px "microsoft yahei"; text-decoration: none; color: #777; background-color: #F5F5F5; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 2px 9px; border: solid 1px; border-color: #DCDCDC #DCDCDC #B3B3B3 #DCDCDC; border-radius: 3px; cursor: pointer; }
a:hover.ncap-btn { text-decoration: none; color: #333; background-color: #E6E6E6; border-color: #CFCFCF #CFCFCF #B3B3B3 #CFCFCF; }
a.ncap-btn-big { font: bold 14px/20px "microsoft yahei", arial; color: #777; background-color: #ECF0F1; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 7px 19px; border: solid 1px #BEC3C7; border-radius: 3px; cursor: pointer; }
a:hover.ncap-btn-big { text-decoration: none; color: #FFF; background-color: #BEC3C7; box-shadow: 0 0 0 2px rgba(82, 168, 236, 0.15); }
a.ncap-btn-mini i,
a.ncap-btn i,
a.ncap-btn-big i { font-size: 14px; vertical-align: baseline; margin-right: 4px; }
a.ncap-btn-blue,
a.ncap-btn-acidblue,
a.ncap-btn-green,
a.ncap-btn-orange,
a.ncap-btn-red,
a.ncap-btn-black,
a:hover.ncap-btn-blue,
a:hover.ncap-btn-acidblue,
a:hover.ncap-btn-green,
a:hover.ncap-btn-orange,
a:hover.ncap-btn-red,
a:hover.ncap-btn-black,
.nscs-table-handle a.btn-orange-current { color: #FFF; text-shadow: 0 -1px 0 rgba(0,0,0,0.10); }
a.ncap-btn-blue { background-color: #3598DC; border-color: #2A80B9; }
a.ncap-btn-acidblue,
.nscs-table-handle a:hover.btn-acidblue { background-color: #49AFCD; border-color: #429DB8 #429DB8 #37839A #429DB8; }
a.ncap-btn-green { background-color: #4fc0e8; border-color: #3aa8cf; font-weight: normal;}
a.ncap-btn-orange,
.nscs-table-handle a:hover.btn-orange,
.nscs-table-handle a.btn-orange-current { background-color: #FAA732; margin: 0; border-style: solid; border-width: 1px; border-color: #E1962D #E1962D #BB7D25 #E1962D !important; }
a.ncap-btn-red,
.nscs-table-handle a:hover.btn-red { background-color: #DA4F49; border-color: #C44742 #C44742 #A33B37 #C44742; }
a.ncap-btn-black,
.nscs-table-handle a:hover.btn-black { background-color: #363636; border-color: #313131 #313131 #282828 #313131; }
a:hover.ncap-btn-blue { background-color: #2A80B9; }
a:hover.ncap-btn-acidblue { background-color: #2F96B4; border-color: #2A87A2 #2A87A2 #237087 #2A87A2; }
a:hover.ncap-btn-green { background-color: #3aa8cf; }
a:hover.ncap-btn-orange { background-color: #F89406; border-color: #DF8505 #DF8505 #BA6F04 #DF8505; }
a:hover.ncap-btn-red { background-color: #BD362F; border-color: #AA312A #AA312A #8E2823 #AA312A; }
a:hover.ncap-btn-black { background-color: #222222; border-color: #1F1F1F #1F1F1F #191919 #1F1F1F; }
/*上传按钮*/
.ncap-upload-btn { vertical-align: top; display: inline-block; *display: inline/*IE7*/;
margin-right: 10px; *zoom:1;
position: relative; z-index: 1; }
.ncap-upload-btn .input-file,
.ncap-upload-btn .input-button { width: 84px; height: 26px; position: absolute; z-index: 2; top: 0; left: 0; right: 0; bottom: 0; opacity: 0; filter: alpha(opacity=0); cursor: pointer; }
.ncap-upload-btn .input-button { z-index: 1; }
/*格式化jquery-ui*/
#stat_tabs.ui-corner-all { border-radius: 0!important; margin-top: 10px; }
#stat_tabs.ui-widget-content,
#stat_tabs .ui-widget-content { background: transparent none!important; border: none 0!important; }
#stat_tabs .ui-tabs,
#stat_tabs .ui-tabs .ui-tabs-panel,
#stat_tabs .ui-tabs .ui-tabs-nav { padding: 0!important; }
#stat_tabs .ui-state-default { border: none 0!important; float: none!important; }
#stat_tabs .ui-tabs-active .ui-tabs-anchor { font-size: 14px!important; font-weight: 600!important; color: #777 !important; background-color: #fff !important; border-bottom-color: #fff!important; }
.ui-widget-header { background: transparent none!important; border: none 0!important;/*弹出框标题底色去除*/ }
#ui-datepicker-div { z-index: 99!important;/*日期控件*/ }
.area-region-select select { margin-right: 5px;}
.area-region-select .input-btn { margin-left: 10px;}
/* ==========================
 * 管理平台布局内容
 * ========================== */

/* 头部
******************************/
.admincp-header { width: 100%; position: relative; z-index: 2; height:51px;}
.admincp-header .wraper{box-shadow: 0 2px 4px rgba(0,0,0,.08); height:48px;}
.admincp-name { width: 192px; padding: 10px 0 6px 70px; float: left; text-shadow: 0 1px 3px rgba(0,0,0,0.25); }
.admincp-name em{font: normal 27px "Microsoft Yahei";line-height: 27px;color: #fff;}
.admincp-name h1 { font-family: Verdana, Geneva, sans-serif; font-size: 14px; line-height: 20px; font-weight: 700; color: #FFF; }
.admincp-name h2 { font-size: 12px; line-height: 16px; color: #FFF; }
.nc-module-menu { float: left; overflow: hidden; }
.nc-module-menu ul { }
.nc-module-menu ul li { line-height: 38px; height: 38px;}
.nc-module-menu ul li a { font-size: 15px; line-height: 38px;  display: block; opacity: 0.8;  width:100%; text-align:center; color: #FFF;}
.nc-module-menu ul li.active a {
filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#26000000', endColorstr='#26000000'); background: rgba(0,0,0,0.15); opacity: 1;color:#ff8800  }
.nc-module-menu ul li a:hover {
filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#26FFFFFF', endColorstr='#26FFFFFF'); background: rgba(255,255,255,0.15); opacity: 1; }
.admincp-header-r { float: right; }
.admincp-header-r .manager,
.admincp-header-r .operate { vertical-align: top; display: inline-block; *display: inline;
*zoom: 1;
}
.admincp-header-r .manager { height: 30px; padding: 6px 0; position: relative; z-index: 2; }
.admincp-header-r .manager dl {text-align: right; vertical-align: top; display: inline-block; *display: inline;
margin-right: 10px; *zoom: 1;
}
.admincp-header-r .manager dt { line-height: 16px;font-size: 14px; }
.admincp-header-r .manager dd { line-height: 16px; }
.admincp-header-r .manager .avatar { background-color: transparent; vertical-align: top; display: inline-block; *display: inline;
width: 32px; height: 34px;  *zoom: 1;
position: relative; z-index: 1; overflow: hidden; }
.admincp-header-r .manager .avatar .admin-avatar-file { width: 32px; height: 32px; opacity: 0; filter: alpha(opacity=0); position: absolute; z-index: 2; top: 1px; left: 1px; cursor: pointer; }
.admincp-header-r .manager .avatar img { height: 32px; width:32px;  position: absolute; z-index: 1; top: 2px; left: 0;border-radius: 18px; }
.admincp-header-r .manager .arrow { font-size: 0px; line-height: 0; vertical-align: top; display: inline-block; width: 0px; height: 0px; margin-left: 10px; margin-top: 15px; margin-right: 10px; border-width: 4px; border-color: #333 transparent transparent transparent; border-style: solid dashed dashed dashed; cursor: pointer; }
.admincp-header-r .manager .arrow-close { font-size: 0px; line-height: 0; vertical-align: top; display: inline-block; width: 0px; height: 0px; margin-left: 10px; margin-top: 10px; margin-right: 10px; border-width: 4px; border-color: transparent transparent #333 transparent; border-style: dashed dashed solid dashed; cursor: pointer; }
.admincp-header-r .manager-menu { background-color: #FFF; display: none; min-width: 220px; padding: 0 8px 8px 8px; border: solid 1px #D7D7D7; position: absolute; z-index: 2; right: 2px; top: 44px; box-shadow: 0 0 4px rgba(0,0,0, 0.25); }
.admincp-header-r .manager-menu .title { line-height: 22px; height: 22px; margin-top: 8px; margin-bottom: 2px; border-bottom: dotted 1px #DDD; overflow: hidden; }
.admincp-header-r .manager-menu .title h4 { font-weight: 600; font-size: 12px; color: #333; display: inline-block; }
.admincp-header-r .manager-menu .title a { font-size: 0; background: transparent url(../images/combine_img.png) no-repeat; width: 60px; height: 16px; float: right; margin-top: 4px; border-radius: 2px; }
.admincp-header-r .manager-menu .title a:hover { background-color: #999; }
.admincp-header-r .manager-menu .title a.edit-password { background-position: 0px -260px; }
.admincp-header-r .manager-menu .title a.add-menu { background-position: -60px -260px; }
.admincp-header-r .manager-menu .title a:hover.edit-password { background-position: -0px -240px; }
.admincp-header-r .manager-menu .title a:hover.add-menu { background-position: -60px -240px; }
.admincp-header-r .manager-menu .login-date { font-size: 11px; font-family: Tahoma; color: #555; padding: 0 8px; }
.admincp-header-r .manager-menu .login-date span { color: #999; display: block; }
.admincp-header-r .manager-menu li { width: 50%; }
.admincp-header-r .manager-menu li a { line-height: 24px; color: #777; padding-left: 8px; }
.admincp-header-r .operate { height: 48px; border-left: solid 1px rgba(255,255,255,0.25); box-shadow: -1px 0 0 rgba(0,0,0,0.15); }
.admincp-header-r .operate li { width: 48px; position: relative; z-index: 1; }
.admincp-header-r .operate li:hover {
filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#26FFFFFF', endColorstr='#26FFFFFF'); background: rgba(255,255,255,0.15); }
.admincp-header-r .operate li a { font-size: 0; background: url(../images/combine_img.png) no-repeat; display: block; width: 48px; height: 48px; position: relative; z-index: 1; }
.admincp-header-r .operate li a.sitemap { background-position: 0 -180px; }
.admincp-header-r .operate li a.style-color { background-position: -50px -180px; }
.admincp-header-r .operate li a.homepage { background-position: -100px -180px; }
.admincp-header-r .operate li a.login-out { background-position: -150px -180px; }
.admincp-header-r .operate li a.toast { background-position: -192px -180px; }
.admincp-header-r .operate li a em,.update em { font-size: 10px; font-weight: 600; line-height: 14px; color: #FFF; background-color: #F30; text-align: center; display: block; min-width: 10px; height: 14px; padding: 0 2px; border: solid 2px #FFF; border-radius: 9px; position: absolute; z-index: 1; top: 2px; right: 1px; -webkit-animation: twinkling 1s infinite ease-in-out; }
.update em{ float:right;margin: 16px 20px 0 0;}
 @-webkit-keyframes 'twinkling' { /*透明度由0到1*/
0% {
opacity:0;  /*透明度为0*/
}
100% {
opacity:1;  /*透明度为1*/
}
}
.admincp-header-r .operate li .sub-menu { position: absolute; z-index: 1; }
/* 头部样式 */
div.bgSelector { width: 100%; text-align: left; display: none; }
div.bgSelector div#bgColorSelector { width: 100%; overflow: hidden; }
div.bgSelector div#bgColorSelector #bscParent { position: relative; width: 100%; height: 100%; overflow: hidden; z-index: 9998; }
div.bgSelector div#bgColorSelector #bscParent #bscDrag img { position: relative; z-index: -1; width: 47px; height: 35px; cursor: ew-resize }
div.bgSelector div#bgColorSelector #bscParent #bscDrag { position: absolute; left: 0; float: left; z-index: 9999; }
div.bgSelector div#bgColorSelector #bscParent #bscDrop { width: 100%; margin: 0; padding: 0; }
div.bgSelector div#bgColorSelector #bscParent #bscDrop td { margin: 0; padding: 0; }
div.bgSelector div#bgColorSelector #bscParent #bscDrop td span { display: block; width: 100%; }
.alert_img { position: absolute; top: 85px; z-index: 9; left: 736px; }
div.nav2Panel { width: 890px; height: 38px; padding: 0 30px; z-index: 999; margin: -5px auto 0; background-color: #fcfcfc; border: 1px #dee0df solid; border-top: 0; }
#foldSidebar { width: 16px; height: 16px; position: absolute; z-index: 99;bottom: 20px;left: 18px; }
#foldSidebar i { cursor: pointer; background: url(../images/sprite_leftMenu.png) no-repeat 0 -50px;height: 18px;width: 20px; display: inline-block; }
/* 容器
******************************/
.admincp-container { background-color: transparent; height: 100%; position: relative; z-index: 4; }

.admincp-container-right { background-color: #FFF; position: absolute; z-index: 1; top: 0px; right: 0; bottom: 0; left: 200px; }
.admincp-container-right .top-border {}
.fold .admincp-container-right { background-color: #FFF; position: absolute; z-index: 1; top: 0px; right: 0; bottom: 0; left: 0px; }
.flexigrid { margin-top: 10px; position: relative; z-index: 1; overflow: hidden; }
/*f表格标题块*/
.flexigrid .mDiv { background-color: #FFF; color: #333; white-space: nowrap; display: block;  position: relative; z-index: 4; padding: 16px 0px;min-height: 30px; }
/*标题*/
.flexigrid .mDiv .ftitle { font-size: 0; *word-spacing:-1px/*IE6、7*/;
height: 24px; float: left; }
.flexigrid .mDiv .ftitle h3,
.flexigrid .mDiv .ftitle h5 { font-size: 12px; font-weight: normal; line-height: 24px; color: #777; vertical-align: bottom; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
padding: 0; *zoom:1/*IE7*/; 
}
.flexigrid .mDiv .ftitle h3 { font-size: 14px; color: #333; margin-right: 6px; display: inline-block;
    text-indent: 8px;
    border-left: 2px solid #88B7E0;}
/*数据统计数*/
.flexigrid .pReload { color: #999; background-color: #FFF; width: 24px; height: 24px; float: left; text-align: center; line-height: 24px; margin: 0 0 0 10px; position: relative; z-index: 1; }
.flexigrid .pReload:hover { color: #333; background-color: #E6E6E6; border-radius: 5px; cursor: pointer; }
.flexigrid .pReload i { font-size: 14px; margin: 0; }
.flexigrid .pReload.loading i { color: #090; display: inline-block; -webkit-animation: fa-spin 0.2s infinite linear; animation: fa-spin 0.2s infinite linear; }
@-webkit-keyframes fa-spin {
0% {
-webkit-transform: rotate(0deg);
transform: rotate(0deg);
}
100% {
-webkit-transform: rotate(359deg);
transform: rotate(359deg);
}
}
@keyframes fa-spin {
0% {
-webkit-transform: rotate(0deg);
transform: rotate(0deg);
}
100% {
-webkit-transform: rotate(359deg);
transform: rotate(359deg);
}
}
/*操作间隔*/
.flexigrid .mDiv .sline { width: 0; height: 16px; float: left; margin: 4px; border-left: dotted 1px #D7D7D7; }
/*操作按钮*/
.flexigrid .nBtn { color: #777; background-color: #FFF; width: 100px; height: 24px; float: left; position: relative; z-index: 1; cursor: pointer; }
.flexigrid .nBtn .hBtn { text-align: center; line-height: 24px; width: 24px; height: 24px; border: solid 1px transparent; position: absolute; z-index: 2; top: 0; left: -1px; }
.flexigrid .nBtn:hover .hBtn { color: #333; background-color: #FFF; border-radius: 4px 4px 0 0; border-color: #D7D7D7; border-bottom: 0; }
.flexigrid .nBtn i { font-size: 14px; margin: 0; }
/*菜单配置下拉框*/
.flexigrid .nDiv { background-color: #FFF; padding: 5px 0 5px 5px; border: 1px solid #D7D7D7; border-radius: 0 0 4px 4px; overflow: auto; position: absolute; z-index: 1; top: 23px; left: -1px; box-shadow: 3px 3px 0 rgba(0,0,0,0.05); }
.flexigrid .nDiv h5 { font-size: 12px; font-weight: normal; line-height: 24px; color: #F60; }
.flexigrid .nDiv .ps-container { display: block; max-height: 150px; padding: 0 10px 10px 0; overflow: hidden; position: relative; z-index: auto; }
.flexigrid .nDiv ul { width: 100%; }
.flexigrid .nDiv li { font-size: 0; *word-spacing:-1px/*IE6、7*/;
line-height: 20px; text-align: left; padding: 2px 10px 2px 0; border-bottom: dotted 1px #E6E6E6; }
.flexigrid .nDiv li:hover { color: #666; background: #F5F5F5; }
.flexigrid .nDiv li span { font-size: 12px; vertical-align: middle; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/;
}
.flexigrid .nDiv li span.ndcol1 { margin-right: 5px; }
.flexigrid .nDiv .ps-scrollbar-x-rail { display: none !important; }
/*表格搜索*/
.flexigrid .sDiv { float: right; position: relative; overflow: hidden; }
.flexigrid .sDiv2 { font-size: 0; *word-spacing:-1px/*IE6、7*/;
display: inline-block; vertical-align: middle; *display: inline/*IE7*/;
*zoom:1/*IE7*/;
border: solid 1px #D7D7D7; }
.flexigrid .sDiv2:hover { border-color: #e7e7e7; }
.flexigrid .sDiv2 .qsbox,
.flexigrid .sDiv2 .select,
.flexigrid .sDiv2 .btn { border: none; border-radius: 0; font-size: 12px; vertical-align: top; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/;
border-radius: 0; }
.flexigrid .sDiv2 .select { background-color: #FFF; width: auto; height: 26px; margin: 0px !important; }
.flexigrid .sDiv2 .qsbox { background-color: #FFF; font-size: 12px; line-height: 24px; width: 120px; height: 24px; padding: 1px 5px; }
.flexigrid .sDiv2  .sDiv3{width: 100px;}
.flexigrid .sDiv2 .qsbox:hover, .flexigrid .sDiv2 .qsbox:fouce {
box-shadow: none;
}
.flexigrid .sDiv2 .btn { height: 26px; cursor: pointer; border-left: solid 1px #D7D7D7; }

.flexigrid .sDiv2 .btn:hover { color: #FFF; background-color: #FF6666; }
.flexigrid .sDiv .a_search { color: #FF6666; display: inline-block; vertical-align: middle; *display: inline/*IE7*/;
margin-left: 8px; *zoom:1/*IE7*/;
}
.flexigrid .sDiv .a_search i { font-size: 14px; margin-right: 4px; }
.flexigrid .sDiv .a_search:hover { color: #CD0200; }

@media screen and (max-width: 1280px){
    .flexigrid .sDiv2 .qsbox { width: 100px;}
    .flexigrid .sDiv2  .sDiv3{width: 90px;}
}
@media screen and (max-width: 1180px){
 .flexigrid .sDiv2 .qsbox { width: 90px;}
}
@media screen and (max-width: 1080px){
 .flexigrid .sDiv2 .qsbox { width: 72px;}
 .flexigrid .sDiv2  .sDiv3{width: 80px;}
}

/*数据表格头*/
.flexigrid .hDiv { background-color: #f7f7f7; clear: both; border:1px solid #f7f7f7; border-bottom: none; position: relative; z-index: 1; overflow: hidden; }
.flexigrid .hDiv table { }
.flexigrid .hDiv th div { }
.flexigrid .hDivBox { float: none; }
.flexigrid .hDiv i.ico-check { background: url(../images/flexigrid_pic.png) no-repeat 0 0; display: inline-block; width: 20px; height: 20px; cursor: pointer; }
.flexigrid .hDiv .trSelected i.ico-check { background-position: -20px 0; }
.flexigrid .hDiv .sorted { background-color: #E6E6E6; }
.flexigrid .hDiv .thOver { }
.flexigrid .hDiv .thOver div,
.flexigrid .hDiv .sorted.thOver div { padding-bottom: 6px; border-bottom: 2px solid #3b639f; cursor: pointer; }
.flexigrid .hDiv .sorted div { padding-bottom: 5px; }
.flexigrid .hDiv .thMove { background: #fff; color: #fff; }
.flexigrid .hDiv .sorted.thMove div { border-bottom: 1px solid #fff; padding-bottom: 4px }
.flexigrid .hDiv .thMove div { background: #fff !important; }
.flexigrid .hDiv th .sdesc { background: url(../images/dn.png) no-repeat center top; }
.flexigrid .hDiv th .sasc { background: url(../images/up.png) no-repeat center top; }
/*调节表格列宽*/
.flexigrid .cDrag { float: left; position: absolute; z-index: 2; overflow: visible; }
.flexigrid .cDrag div { background: none; display: block; width: 3px; height: 24px; float: left; position: absolute; cursor: col-resize; }
.flexigrid .cDrag div:hover,
.flexigrid .cDrag div.dragging {
filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#19000000', endColorstr='#19000000'); background: rgba(0,0,0,0.1); border: solid #FFF; border-width: 0 1px; }
/*批量操作条*/
.flexigrid .tDiv { position: relative; z-index: 3; overflow: hidden;  background-color: #f7f7f7; border: 1px solid #f7f7f7; border-top: none;}
.flexigrid .tDiv2 { font-size: 0; *word-spacing:-1px/*IE6、7*/;
padding: 6px 0 6px 6px; float: left; overflow: hidden; }
.flexigrid .btnseparator { float: left; height: 22px; border-left: 1px solid #ccc; border-right: 1px solid #fff; margin: 1px; }
.flexigrid .fbutton { vertical-align: top; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
padding-right: 8px; margin-left: 8px; margin-right: -1px;  *zoom:1/*IE7*/;
cursor: pointer; }
.flexigrid .fbutton div { font-size: 12px; font-family: 宋体; line-height: 20px; color: #333;border: 1px solid #ddd;background-color: #fdfdfd;padding: 3px 9px; border-radius: 5px;}

.flexigrid .fbutton div.dx{ color: #ff0000; }
.flexigrid .fbutton .add:hover,
.flexigrid .fbutton.fbOver .add:hover {background: #fff;}
.flexigrid .fbutton .del:hover,
.flexigrid .fbutton.fbOver .del:hover { color: #E84C3D; border-color: #E84C3D; }
.flexigrid .fbutton .csv:hover,
.flexigrid .fbutton.fbOver .csv:hover { color: #9C59B8; border-color: #9C59B8; }
/*数据表格内容部分*/
.flexigrid .bDiv { background: #fff; position: relative; z-index: 2; overflow: auto; border-left: 1px solid #f7f7f7; border-right: 1px solid #f7f7f7;}
.flexigrid .bDiv table { width: 100%; }
.flexigrid .bDiv td { color: #555; vertical-align: top; white-space: nowrap; padding: 0; margin: 0; border-bottom: 1px solid E6E6E6; }
.flexigrid .bDiv td.name div{ white-space: nowrap;text-overflow:ellipsis; }
.flexigrid .bDiv td.sign input,.flexigrid .hDiv th.sign input{ margin: 5px 8px; }
.flexigrid .bDiv td div {}
.flexigrid .bDiv a { color: #3b639f; }
.flexigrid .bDiv a:hover { text-decoration: underline;}
.flexigrid .bDiv td i.ico-check { background: url(../images/flexigrid_pic.png) no-repeat 0 0; display: inline-block; width: 20px; height: 20px; }
.flexigrid .bDiv .trSelected td i.ico-check { background-position: -20px 0; }
.flexigrid .bDiv td .on,
.flexigrid .bDiv td .yes { color: #41BEDD; cursor: default; }
.flexigrid .bDiv td .off,
.flexigrid .bDiv td .no { color: #9ea3a7; cursor: default; }
.flexigrid .bDiv td.normal { color: #C30; }
.flexigrid .bDiv td.abnormal { color: #090; }
.flexigrid .bDiv td.column-a { background-color: #F6EEFB; }
.flexigrid .bDiv td.column-b { background-color: #F9F3D7; }
.flexigrid .bDiv td.column-c { background-color: #FBEEE0; }

.flexigrid .bDiv .typename,.flexigrid .hDiv th div.sunone{ padding-left: 15px; }

/*数据表格查看操作项*/
.flexigrid .bDiv .handle div { font-size: 0; *word-spacing:-1px/*IE6、7*/;
}
.flexigrid .bDiv a.btn , a.ui-btn{  font-weight: normal; line-height: 20px; color: #777; background: #FFF none; vertical-align: top; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/;
height: 20px; padding: 1px 6px;  cursor: pointer !important; }
a.ui-btn{ height: 26px; line-height: 26px; font-size: 12px;}
.flexigrid .bDiv a.btn i{ display: none; }
.flexigrid .bDiv a.btn:hover,a.ui-btn:hover { color: #555; text-decoration: none;}
.flexigrid .bDiv a.red:hover,a.ui-btn.red:hover{ color: #FFF; background-color: rgba(196,24,45,0.8); border-color: rgba(196,24,45,0.8); }
.flexigrid .bDiv a.blue:hover,a.ui-btn.blue:hover { color: #FFF; background-color: rgba(53,152,220,0.8); border-color: rgba(53,152,220,0.8); }
.flexigrid .bDiv a.green:hover { color: #FFF; background-color: #41BEDD; border-color: #41BEDD; }
.flexigrid .bDiv a.orange:hover { color: #FFF; background-color: #FFBF40; border-color: #FFBF40; }
.flexigrid .bDiv a.purple:hover { color: #FFF; background-color: #9C59B8; border-color: #8F44AD; }
.flexigrid .bDiv a.pomegranate:hover { }
.flexigrid .bDiv a.belize-hole:hover { color: #FFF; background-color: #E84C3D; border-color: #C1392B; }
.flexigrid .bDiv .handle span { font-size: 12px; }
.flexigrid .bDiv span.btn { font-size: 12px; vertical-align: top; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/;
position: relative; z-index: 1; cursor: default; }
.flexigrid .bDiv span.btn:hover { z-index: 3; box-shadow: 2px 2px 0 rgba(0,0,0,0.1); }
.flexigrid .bDiv span.btn em { color: #777; line-height: 20px; background-color: #FFF; display: block; height: 20px; padding: 1px 6px; border: solid 1px #F5F5F5; border-radius: 4px 0 0 4px; position: relative; z-index: 2; }
.flexigrid .bDiv span.btn:hover em { color: #FFF; background-color: #4fc0e8; border-color: #3aa8cf; }
.flexigrid .bDiv span.btn em i { font-size: 14px; vertical-align: middle; margin-right: 4px; }
.flexigrid .bDiv span.btn em .arrow { font-size: 0px; line-height: 0; vertical-align: middle; display: inline-block; width: 0px; height: 0px; float: none; margin: 0 0 0 4px; border-width: 4px; border-color: #999 transparent transparent transparent; border-style: solid dashed dashed dashed; -webkit-transition: .2s ease-in; -moz-transition: -webkit-transform .2s ease-in; -o-transition: -webkit-transform .2s ease-in; transition: .2s ease-in; }
.flexigrid .bDiv span.btn:hover .arrow { border-color: #FFF transparent transparent transparent; FILTER: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
-moz-transform: rotate(270deg); -moz-transform-origin: 50% 30%; -webkit-transform: rotate(270deg); -webkit-transform-origin: 50% 30%; -o-transform: rotate(270deg); -o-transform-origin: 50% 30%; transform: rotate(270deg); transform-origin: 50% 30%; }
.flexigrid .bDiv span.btn ul { background-color: #4fc0e8; display: none; border: solid 1px #3aa8cf; border-radius: 0 4px 4px 0; margin-left: -2px; position: absolute; z-index: 1; top: 0; left: 100%; overflow: hidden; box-shadow: 3px 3px 0 rgba(0,0,0,0.1); }
.flexigrid .bDiv tr:last-child span.btn ul { bottom: 0; top: auto; }
.flexigrid .bDiv tr:first-child span.btn ul { bottom: auto; top: 0; }
.flexigrid .bDiv tr:nth-last-child(2) span.btn ul { bottom: auto; top: 0; }
.flexigrid .bDiv span.btn:hover ul { display: block; padding: 0 5px; }
.flexigrid .bDiv span.btn ul li { border-bottom: dotted 1px #3aa8cf; clear: both; margin-bottom: -1px; }
.flexigrid .bDiv span.btn ul li a { color: #FFF; display: block; line-height: 24px; text-align: right; height: 24px; padding: 0 5px; }
.flexigrid .bDiv span.btn ul li a:hover { text-decoration: underline; }
.flexigrid .bDiv span.btn ul li a.expire { color: #FF0 !important; }
.flexigrid .bDiv a.expired { color: #F30; }
.flexigrid .bDiv a.expire { color: #F93; }
i.fa-external-link { color: #999 !important; font-size: 9px !important; margin-left: 4px; margin-right: 0; }
.flexigrid .bDiv .user-avatar { background-color: #FFF; vertical-align: middle; display: inline-block; width: 22px; height: 22px; margin-right: 6px; border: solid 1px #D7D7D7; border-radius: 50%; }
.flexigrid .bDiv .pic-thumb-tip { color: #777; }
.flexigrid .bDiv .pic-thumb-tip i { font-size: 14px; }
.flexigrid .bDiv .pic-thumb-tip:hover { color: #333; }
/*数据表格查询为空*/
.no-data,
.flexigrid .bDiv .no-data { font-size: 14px; color: #999; width: 100% !important; height: 24px; border: 0 none !important; text-align: center; padding: 100px 0; }
.flexigrid .bDiv .trSelected .no-data,
.flexigrid .bDiv .no-data:hover { color: #999 !important; background-color: transparent !important; }
.no-data i,
.flexigrid .bDiv .no-data i { font-size: 18px; margin-right: 6px; color: #FC0; }
.flexigrid .bDiv .handle-btn { }
/*hDiv\bDiv\colCopy同步项*/
.flexigrid .hDiv th,
.flexigrid .bDiv td { vertical-align: top !important; text-align:left; font-size: 14px !important; overflow: visible;  border-bottom: 1px solid #f7f7f7;font-family: "Segoe UI", "Lucida Grande", Helvetica, Arial, "Microsoft YaHei", FreeSans, Arimo, "Droid Sans","wenquanyi micro hei","Hiragino Sans GB", "Hiragino Sans GB W3", Arial, sans-serif;}
.flexigrid .bDiv td div img{ float: left; }
.flexigrid .hDiv th div,
.flexigrid .bDiv td div,
.colCopy div { display: block; line-height: 22px; text-overflow: ellipsis; white-space: nowrap;  padding: 8px 0px; overflow: hidden; }
.flexigrid .bDiv td div { padding: 14px 0px; white-space: normal; }

.flexigrid .hDiv .handle div,
.flexigrid .bDiv .handle div { overflow: visible; min-width: 150px !important; max-width: 174px !important; }
.flexigrid .hDiv .handle-s div,
.flexigrid .bDiv .handle-s div { overflow: visible; min-width: 60px !important; max-width: 60px !important; }
.flexigrid.hideBody { height: 26px !important; border-bottom: 1px solid #ccc; }
.ie6fullwidthbug { border-right: 0px solid #ccc; padding-right: 2px; }
.flexigrid .bDiv table.autoht { width: 100%; margin-bottom: 0px; border-bottom: 0px; }
.flexigrid .nBtn.srtd { background: url(../images/wbg.gif) repeat-x 0px -1px; }
.flexigrid .hDiv th,
.colCopy {  white-space: nowrap; cursor: default; overflow: hidden; }
.colCopy { color: #333; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#E5FFFFFF', endColorstr='#E5FFFFFF');
background: rgba(255,255,255,0.9); border: dotted 1px #D7D7D7; overflow: hidden; box-shadow: 4px 4px 0 rgba(0,0,0,0.05); z-index: 9; }
.flexigrid tr td.sorted { background-color: #F9F9F9; border-bottom: 1px solid #E6E6E6 }
.flexigrid tr td.sorted div { bckground: #FFF7CF }
.flexigrid tr.erow td { background: #FDFDFD; border-bottom: 1px solid #F5F5F5; }
.flexigrid tr.erow td.sorted { background: #F0F0F0; border-bottom: 1px solid #E6E6E6; }
.flexigrid tr.erow td.sorted div { }
.flexigrid div.bDiv tr:hover td,
.flexigrid div.bDiv tr:hover td.sorted,
.flexigrid div.bDiv tr.trOver td.sorted,
.flexigrid div.bDiv tr.trOver td { background: #F4FCFA; }

.flexigrid .pDiv { background-color: #F5F5F5; border-style: solid; border-color: #D7D7D7 transparent #C8C8C8 transparent; border-width: 1px 0; position: relative; z-index: 3; }
.flexigrid .pDiv .pDiv2 { padding: 6px 0; margin: 0; border-color: #FFF; border-style: solid; border-width: 1px 0; position: relative; z-index: 1; }
.flexigrid .pGroup-left { color: #777; line-height: 24px; height: 24px; position: absolute; z-index: 1; top: 6px; left: 12px; }
.flexigrid .pGroup-left .select { font-family: Arial, Helvetica, sans-serif; vertical-align: middle; display: inline-block; *display: inline;
width: 40px; height: 24px; background-color: transparent; padding: 0; margin: 0 6px 0 0; border-radius: 0; border: none; *zoom: 1;
}
.flexigrid .pGroup-right { color: #777; line-height: 24px; height: 24px; position: absolute; z-index: 1; top: 6px; right: 12px; }
/*f表格翻页控制*/
.flexigrid .pGroup-middle { font-size: 0; *word-spacing:-1px/*IE6、7*/;
text-align: center; margin: 0 auto; width: 250px; height: 24px; float: none; z-index: auto; }
.flexigrid .pGroup-middle .pButton,
.flexigrid .pGroup-middle .pcontrol,
.flexigrid .pGroup-middle .pcontrol input,
.flexigrid .pGroup-middle .pcontrol span { vertical-align: middle; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/;
}
.flexigrid .pGroup-middle .pButton { color: #999; background-color: transparent; text-align: center; width: 24px; height: 24px; padding: 0; margin-right: 8px; border-radius: 4px; cursor: pointer; overflow: hidden; }
.flexigrid .pGroup-middle .pButton:hover { color: #333; background-color: #CCC; padding: 0; border: 0; }
.flexigrid .pGroup-middle .pButton i { line-height: 24px; font-size: 14px; }
.flexigrid .pGroup-middle .pcontrol { font-size: 12px; line-height: 20px; color: #999; *word-spacing:-1px/*IE6、7*/;
margin-right: 10px; }
.flexigrid .pGroup-middle .pcontrol input { color: #333; background-color: transparent; text-decoration: underline; text-align: center; width: 20px; padding: 0; border: none; border-radius: 0; }
.flexigrid .pGroup-middle .pcontrol input:hover,
.flexigrid .pGroup-middle .pcontrol input:focus { background-color: #FFF; box-shadow: none; }
.pPageStat { display: block; line-height: 40px; text-align: center; color: #FFF; position: absolute; top: -40px; left: 0px; right: 0px; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#7F000000', endColorstr='#7F000000');
background: rgba(0,0,0,0.5); width: 100%; }
.flexigrid .pGroup-middle .pcontrol span { margin-right: 2px; }
.flexigrid .pDiv .pButton.pBtnOver { width: 20px; height: 20px; border: 1px solid #ccc; cursor: pointer; }
.flexigrid .pDiv .pButton span { display: block; width: 20px; height: 20px; float: left; }
.flexigrid .pDiv .pButton:hover span,
.flexigrid .pDiv .pButton.pBtnOver span { width: 19px; height: 19px; border-top: 1px solid #fff; border-left: 1px solid #fff; }
.flexigrid div.iDiv { border: 1px solid #316ac5; position: absolute; overflow: visible; background: none; }
.flexigrid div.iDiv input,
.flexigrid div.iDiv select,
.flexigrid div.iDiv textarea { font-family: Arial, Helvetica, sans-serif; font-size: 11px; }
.flexigrid div.iDiv input.tb { border: 0px; padding: 0px; width: 100%; height: 100%; padding: 0px; background: none; }
.flexigrid div.hGrip { position: absolute; top: 0px; right: 0px; height: 5px; width: 5px; background: url(../images/line.gif) repeat-x center; margin-right: 1px; cursor: col-resize; }
.flexigrid div.hGrip:hover,
.flexigrid div.hGrip.hgOver { border-right: 1px solid #999; margin-right: 0px; }
.flexigrid div.vGrip { height: 5px; overflow: hidden; position: relative; background: #fafafa url(../images/wbg.gif) repeat-x 0px -1px; border: 1px solid #ccc; border-top: 0px; text-align: center; cursor: row-resize; display: none; }
.flexigrid div.vGrip span { display: block; margin: 1px auto; width: 20px; height: 1px; overflow: hidden; border-top: 1px solid #aaa; border-bottom: 1px solid #aaa; background: none; }
.flexigrid span.cdropleft { display: block; background: url(../images/prev.gif) no-repeat -4px center; width: 24px; height: 24px; position: relative; top: -24px; margin-bottom: -24px; z-index: 3; }
.flexigrid div.hDiv span.cdropright { display: block; background: url(../images/next.gif) no-repeat 12px center; width: 24px; height: 24px; float: right; position: relative; top: -24px; margin-bottom: -24px; }
/* ie adjustments */
.flexigrid.ie .hDiv th div,
.colCopy.ie div/* common inner cell properties*/ { overflow: hidden; }
/* 同步调用表格时搜索栏 */
.ncap-search-ban-s { color: #FFF; background-color: #16a086; width: 16px; padding: 8px 3px 8px 5px; margin-top: -80px; border: solid 1px #16a086; border-right: 0 none; position: fixed; display: block; z-index: 99; top: 50%; right: 0; cursor: pointer; box-shadow: 0 0 5px 0 rgba(204,204,204,0.5); }
.ncap-search-bar-s i { margin: 0 0 5px 0; }
.ncap-search-bar { background-color: #F5F5F5; border-left: solid 1px #D7D7D7; height: 100%x; padding: 10px 0 10px 10px; position: fixed; z-index: 99; top: 0; bottom: 0; right: -230px; }
.ncap-search-bar .title { display: block; }
.ncap-search-bar .title h3 { color: #333; font-size: 16px; font-weight: normal; line-height: 20px; }
.ncap-search-bar .handle-btn { color: #999; background-color: #F5F5F5; width: 16px; padding: 8px 3px 8px 5px; margin-top: -80px; border: solid 1px #E7E7E7; border-right: 0 none; position: absolute; z-index: 1; left: -25px; top: 50%; cursor: pointer; }
.ncap-search-bar .handle-btn i { margin-bottom: 5px; }
.ncap-search-bar .content { width: 156px; display: block; padding-right: 15px; margin-bottom: 50px; position: relative; z-index: 1; overflow: hidden; }
.ncap-search-bar .content .layout-box { display: block; }
.ncap-search-bar dl { padding: 5px 0; border-bottom: solid 1px #E6E6E6; box-shadow: 0 1px 0 rgba(255,255,255, 0.75); }
.ncap-search-bar dt { line-height: 20px; color: #808B8D; padding: 0 0 2px 4px; }
.ncap-search-bar dd { }
.ncap-search-bar dd label { display: block; margin-bottom: 5px; }
.ncap-search-bar dd .s-input-txt { background-color: #FFF; width: 140px; }
.ncap-search-bar dd .s-select { width: 150px; }
.ncap-search-bar dd .querySelect,
.ncap-search-bar dd .class-select { width: 150px; margin-bottom: 5px; }
.ncap-search-bar .bottom { background-color: #F5F5F5; padding: 10px 0 15px 0; border-top: solid 1px #E7E7E7; text-align: center; position: absolute; z-index: 2; left: 0; right: 0; bottom: 0; }
.ncap-form-default { padding: 10px 0; overflow: hidden; }
.ncap-form-default .title { padding: 10px 0; border-bottom: solid 1px #C8C8C8; }
.ncap-form-default .title h3 { font-size: 16px; line-height: 20px; color: #333; font-weight: normal; }
.ncap-form-default dl.row { font-size: 0; color: #777; background-color: #FFF; *word-spacing:-1px/*IE6、7*/;
padding: 12px 0; margin-top: -1px; border-style: solid; border-width: 1px 0; border-color: #f7f7f7; position: relative; z-index: 1; }
.ncap-form-default dl.row:first-child { border-top-color: #FFF; }
.ncap-form-default dl.row:nth-child(even) { background-color: #FDFDFD; }
.ncap-form-default dt.tit,
.ncap-form-default dd.opt { font-size: 12px; line-height: 24px; vertical-align: top; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/; font-size:14px;
}
.ncap-form-default dt.tit { text-align: left; width: 100px; padding-left: 34px; }
.ncap-form-default dd.opt { text-align: left; width: 83%; }
.ncap-form-default dt.tit em { font: bold 14px/20px tahoma, verdana; color: #F60; vertical-align: middle; display: inline-block; margin-right: 5px; margin-left: -14px; }
.ncap-form-default .input-txt { width: 374px !important; }
#web_name{ width:280px !important;}
.ncap-form-default ul.list { }
.ncap-form-default ul.list li { clear: both; }
.ncap-form-default .input-btn { font-size: 12px; background-color: #F5F5F5; vertical-align: top; display: inline-block; height: 26px; padding: 0 10px; border: solid 1px #D7D7D7; border-radius: 4px; cursor: pointer; }
.ncap-form-default .input-btn:hover { text-decoration: none; color: #333; background-color: #E6E6E6; border-color: #A9A9A9; }
.ncap-form-default p.notic { line-height: 12px; line-height: 18px; color: #AAA; margin-top: 4px; clear: both; display: none;}
.ncap-form-default dl.row:hover p.notic { color: #00B7EE; }
.ncap-form-default div.bot { display: block; padding: 12px 0 10px 135px; }
/*弹出框体下的特殊性*/
.dialog_content .ncap-form-default,
.dialog_content .ncap-form-all { width: 96%; margin: 0 auto; padding: 0; }
.dialog_content .ncap-form-default dt.tit { text-align: right; width: 20%; padding-right: 2%; padding-left: 0px;}
.dialog_content .ncap-form-default dd.opt { text-align: left; width: 76%; }
.dialog_content .ncap-form-all dl.row { padding: 8px 0; }
.dialog_content .ncap-form-all dt.tit { font-size: 12px; font-weight: 600; line-height: 24px; background-color: transparent; height: 24px; padding: 4px; }
.dialog_content .ncap-form-all dd.opt { font-size: 12px; padding: 0; border: none; }
.dialog_content .ncap-form-all .search-bar { padding: 4px; }
.dialog_content .bot { text-align: center; padding: 12px 0 10px 0 !important; }
.dialog_content .rule-goods-list { position: relative; z-index: 1; overflow: hidden; max-height: 200px; }
.dialog_content .rule-goods-list ul { font-size: 0; }
.dialog_content .rule-goods-list ul li { font-size: 12px; vertical-align: top; display: inline-block; width: 48%; padding: 1%; }
.dialog_content .rule-goods-list ul li img { float: left; width: 32px; height: 32px; margin-right: 5px; }
.dialog_content .rule-goods-list ul li a,
.dialog_content .rule-goods-list ul li span { color: #555; line-height: 16px; white-space: nowrap; text-overflow: ellipsis; display: block; float: left; width: 180px; height: 16px; overflow: hidden; }
.dialog_content .rule-goods-list ul li span { color: #AAA; }
/*权限组*/
.ncap-account-all { padding-left: 1%; }
.ncap-account-container { line-height: 20px; display: block; min-height: 20px; padding: 15px 0 10px 0; border-top: dotted 1px #CCC; }
.ncap-account-container:nth-child(even) { background: #FDFDFD; }
.ncap-account-container:hover { background: #F4FCFA; }
.ncap-account-container h4 { font-size: 12px; font-weight: normal; color: #777; text-align: right; vertical-align: top; display: inline-block; *display: inline/*IE7*/;
width: 11%; margin-right: 1%; *zoom: 1;
}
.ncap-account-container-list { font-size: 0; *word-spacing:-1px/*IE6、7*/;
vertical-align: top; display: inline-block; *display: inline/*IE7*/;
width: 86%; padding-left: 1%; border-left: dotted 1px #CCC; *zoom: 1;
}
.ncap-account-container-list li { font-size: 12px; line-height: 20px; color: #999; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/;
width: 150px; height: 20px; margin-bottom: 5px; *zoom: 1;
}
/*运单模板设计微调*/
.ncap-waybill-list { }
.ncap-waybill-list li { width: 19%; padding: 0 1% 8px 0; }
.ncap-waybill-list li i { cursor: pointer; }
/*内容页面样式
------------------------------------------------------------------- */
.page { background-color: #FFF; min-width: 1000px; padding: 8px 1% 0 1%; text-align: left; overflow: hidden; }
.fixed-bar { background-color: #FFF; width: 100%; padding-bottom: 4px; z-index: 99; top: 0; left: 0;}
.item-title { line-height: 20px; white-space: nowrap; width: 100%; padding-top: 3px;  border-bottom: solid 1px #E6E6E6; }
.item-title .subject { vertical-align: bottom; display: inline-block; *display: inline;
*zoom: 1;height: 26px; padding: 6px 0; margin-right: 18px; }
.item-title h3 { font-size: 17px; font-weight: normal; line-height: 20px; color: #333; display: inline-block;}
.item-title h5 { font-size: 12px; font-weight: normal; line-height: 18px; color: #777; display: inline-block;margin-left: 10px }
.tab-base { vertical-align: bottom; display: inline-block; *display: inline;
*zoom: 1;
}
.tab-base li { height: 35px; margin-right: 5px; }
.tab-base a { line-height: 20px !important; color: #4d4d4d !important; background-color: #EDEDED !important; display: block; !important height: 20px !important;
padding: 7px 20px !important; border: solid 1px #ccc !important; border-radius: 2px 2px 0 0 !important; cursor: pointer; !important
}
.tab-base a:hover { color: #333 !important; }
.tab-base a.current,
.tab-base a:hover.current { background-color: #FFF !important; border-bottom-color: #FFF !important; cursor: default !important; }
.item-title a.back { color: #999; display: inline-block; vertical-align: bottom; margin: 0 0px 6px 0; }
.item-title a.back:hover { color: #4fc0e8; }
.item-title a.back i { font-size: 40px; }
/*注释说明帮助*/
.explanation { color: #0ba4da !important; background-color: rgba(79, 192, 232, 0.11) !important; display: block; width: 99%; height: 100%; padding: 6px 9px; border-radius: 5px; position: relative; overflow: hidden; }
/*.explanation:before{content: "";background-image: url(../images/wave.png); width: 100%;height: 100%;position: absolute;top: 0px;left: 0px;border-radius: 5px;background-repeat: no-repeat;background-size: cover;}*/
.explanation .title { white-space: nowrap; margin-bottom: 8px; position: relative; cursor: pointer; }
.explanation .title h4 { font-size: 14px; font-weight: normal; line-height: 20px; height: 20px; display: inline-block; }
.explanation .title i { font-size: 18px; vertical-align: middle; margin-right: 6px; }
.explanation .title span { background: url(../images/combine_img.png) no-repeat -580px -200px; width: 20px; height: 20px; position: absolute; z-index: 1; top: -6px; right: -9px; }
.explanation ul { color: #748A8F; margin-left: 10px; }
.explanation li { line-height: 20px; background: url(../images/macro_arrow.gif) no-repeat 0 10px; padding-left: 10px; margin-bottom: 4px;  }
/* 宽度\高度\尺寸
------------------------------------------------------------------- */
.w18pre { width: 18%; }
.size-64x64 { width: 64px; height: 64px; }
.size-88x29 { width: 88px; height: 29px; }
.size-72x72 { width: 72px; height: 72px; }
.size-106x106 { width: 106px; height: 106px; }
.red { color: red; }
.blue { color: #06C; }
.orange { color: #F60; }
.bold { font-weight: bold; color: #545454 }
/* tip提示 */
.tip-yellowsimple { color: #000; background-color: #fff9c9; text-align: left; min-width: 50px; max-width: 300px; border: 1px solid #c7bf93; border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px; z-index: 1000; padding: 6px 8px; }
.tip-yellowsimple .tip-inner { font: 12px/16px arial, helvetica, sans-serif; }
.tip-yellowsimple .tip-arrow-top { background: url(../images/tip-yellowsimple_arrows.gif) no-repeat; width: 9px; height: 6px; margin-top: -6px; margin-left: -5px; top: 0; left: 50%; }
.tip-yellowsimple .tip-arrow-right { background: url(../images/tip-yellowsimple_arrows.gif) no-repeat -9px 0; width: 6px; height: 9px; margin-top: -4px; margin-left: 0; top: 50%; left: 100%; }
.tip-yellowsimple .tip-arrow-bottom { background: url(../images/tip-yellowsimple_arrows.gif) no-repeat -18px 0; width: 9px; height: 6px; margin-top: 0; margin-left: -5px; top: 100%; left: 50%; }
.tip-yellowsimple .tip-arrow-left { background: url(../images/tip-yellowsimple_arrows.gif) no-repeat -27px 0; width: 6px; height: 9px; margin-top: -4px; margin-left: -6px; top: 50%; left: 0; }
/* table
------------------------------------------------------------------- */
#gcategory select { margin-right: 3px; }
/* tb */
.rowform { width: 306px; overflow: auto; }
.rowform .txt,
.rowform textarea { margin-right: 10px; width: 250px; }
.rowform .txt2,
.rowform textarea { margin-right: 10px; width: 110px; }
.rowform select { margin-right: 10px; width: 256px; }
.rowform .class-select { width: 90px; margin: 0; }
.rowform .change-select-2 select { width: 123px; } /*2级联动选择*/
.rowform .change-select-3 select { width: 78px; } /*3级联动选择*/
.rowform .radio { margin-top: -2px !important; *margin-top:0 !important;
*margin-top:-2px;
}
.rowform li { overflow: hidden; float: left; margin-right: 10px; white-space: nowrap; cursor: pointer; }
.rowform .clear { clear: both; float: none; margin-bottom: 10px; }
.rowform .nofloat { clear: both; }
.rowform .nofloat li span.radio { line-height: 25px; width: 100px; float: left; }
.rowform .nofloat li select { width: 156px; }
.rowform .nofloat li.left { float: left; }
.rowform .nofloat li { float: none; margin: 5px 0; overflow: visible; }
.ckbox { width: 700px; }
.ckbox li { float: left; margin: 5px 10px 5px 0; white-space: nowrap; width: 130px; height: 20px; }
/* Page title
---------------------------------------------------------------------*/

.txt,
.txt2,
.select { height: 20px; line-height: 20px; }
tr td.handler span { display: block; width: 140px; text-align: left; margin: 0 auto; }
tr.no_data td { font-size: 14px; line-height: 120px; color: #09C; text-align: center; font-weight: bold; }
.stat { float: left; height: 20px; line-height: 20px; color: #a3a3a3; text-decoration: none; }
.select { width: 370px; color: #444; font-size: 12px; }
.wordSpacing5 { word-spacing: 5px; }
.text250 { width: 238px; color: #444; font-size: 12px; padding-left: 18px }
.normal { font-weight: normal; }
.file { width: 330px; }
.floatleft { float: left; padding-left: 15px; }
.clear { clear: both; }
.mt10 { margin-top: 10px; }
.mr10 { margin-right: 10px; }
.sort_order { width: 50px; height: 17px; line-height: 17px; text-align: center }
.order th { border-top: 1px dotted #CBE9F3; font-weight: 700; color: #000; }
.order .noborder th { border-top: none; }
.order .space th { font-size: 14px; padding: 0; }
.order td { }
.order ul { width: 98%; margin: 5px auto; overflow: hidden; }
.order ul li { color: #333; width: 50%; float: left; padding: 4px 0; }
.order ul li strong { font-weight: normal; color: #888; padding: 0 6px 0 0; }
.order .goods { border: solid 1px #CBE9F3; width: 98%; margin: 10px auto; }
.order .goods th { background-color: #F3FBFE; }
.red_common { font-weight: bold; color: #ff5400; }
.red_big { font-weight: bold; color: #ff5400; font-size: 16px; }
form label.error { font-style: normal; font-weight: normal; color: #E84C3D; margin-left: 5px; }
form label.error i { font-size: 14px; margin-right: 4px; }
form input.error,
form textarea.error { background-color: #FFF0F0; background-repeat: repeat; border: 1px dashed #E84C3D; }
/*投诉部分*/

.admin { color: black; }
.accuser { color: red; }
.accused { color: green; }
/* widget */
big,
.big { font-size: 120% !important; line-height: 120%; }
.checked,
.checked .txt { color: #0D0; }
.lightfont { color: #CCC; }
.light,
.light a { color: #AAA; }
.error { color: #F00; }
.nomargin { margin: 0 !important; }
.marginleft { margin-left: 20px; }
.marginright { margin-right: 10px; }
.margintop { margin-top: 10px; }
.marginbot { margin-bottom: 10px; }
.nobg,
.nobg td { background: none; }
.nobdb { border-bottom: none; }
.nobdt { border-top: none; }
.noborder,
.noborder td { border-bottom: 0; border-top: 0; }
.noborder td.tips { color: #999; vertical-align: middle; }
.noborder td.tips:hover,
.normalfont { color: #000; }
.tips a { color: #FFF; background-color: #F60; padding: 2px 4px; margin: 0 4px; border: 1px solid #F30; border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px; /*Firefox\Chrome\Safari\IE9\元素圆角效果*/ }
.vatop { vertical-align: top; }
.lineheight { line-height: 150%; }
.left { float: left; }
.right { float: right; }
.center { text-align: center; }
.alignleft { text-align: left; }
.alignright { text-align: right; }
.bold { font-weight: 700; }
.normal { font-weight: 400; }
.clear { clear: both; }
/* calendar */

.header,
.header td,
.header th { border-top: 1px dotted #DEEFFB; font-weight: 700; }
.thead th { font-size: 13px; font-weight: 700; color: #333; white-space: nowrap; border-top: solid 1px #DEEFFB; }
/* 外边距 Margin，三组从窄到宽，级别分别为：n, m, w */
.mtw { margin-top: 20px !important; }
/* Used for the Switch effect: */
.onoff { font-size: 0; position: relative; overflow: hidden; display: block; float: left; }
.onoff label { vertical-align: top; display: inline-block; *display: inline;
*zoom: 1;
cursor: pointer; }
.onoff input[type="radio"] { position: absolute; top: 0; left: -999px; }
.onoff .cb-enable,
.onoff .cb-disable { color: #777; font-size: 12px; line-height: 20px; background-color: #ECF0F1; height: 20px; padding: 1px 9px; border-style: solid; border-color: #BEC3C7; }
.onoff .cb-enable { border-width: 1px 0 1px 1px; }
.onoff .cb-disable { border-width: 1px 1px 1px 0;  }
.onoff .cb-disable.selected { color: #FFF; background-color: #41BEDD; border-color: #41BEDD; }
.onoff .cb-enable.selected { color: #FFF; background-color: #41BEDD; border-color: #41BEDD }
/* Buttons
---------------------------------------------------------------------*/


.align-center { text-align: center; }
.nowrap { white-space: nowrap; }
.nobg { background: transparent none no-repeat scroll 0 0 !important; }
.space th { font-size: 16px; padding-left: 0 !important; }
table.search { margin: 12px 0 6px 4px; }
.yes-onoff a,
.no-onoff a,
.power-onoff a { line-height: 999%; background: url(../images/combine_img.png) no-repeat scroll; display: inline-block; width: 34px; height: 34px; overflow: hidden; }
.yes-onoff img,
.no-onoff img,
.power-onoff img { }
.yes-onoff a.enabled { background-position: 0px -300px; }
.yes-onoff a:hover.enabled { background-position: -40px -300px; }
.yes-onoff a:active.enabled { background-position: -80px -300px; }
.yes-onoff a.disabled { background-position: -120px -300px; }
.yes-onoff a:hover.disabled { background-position: -160px -300px; }
.yes-onoff a:active.disabled { background-position: -200px -300px; }
.no-onoff a.enabled { background-position: 0px -340px; }
.no-onoff a:hover.enabled { background-position: -40px -340px; }
.no-onoff a:active.enabled { background-position: -80px -340px; }
.no-onoff a.disabled { background-position: -120px -340px; }
.no-onoff a:hover.disabled { background-position: -160px -340px; }
.no-onoff a:active.disabled { background-position: -200px -340px; }
.power-onoff a.enabled { background-position: 0px -380px; }
.power-onoff a:hover.enabled { background-position: -40px -380px; }
.power-onoff a:active.enabled { background-position: -80px -380px; }
.power-onoff a.disabled { background-position: -120px -380px; }
.power-onoff a:hover.disabled { background-position: -160px -380px; }
.power-onoff a:active.disabled { background-position: -200px -380px; }
.msg .tip { line-height: 32px; color: #555; }
.msg .tip2 { line-height: 32px; color: #999; }

.color .colorPicker { vertical-align: middle; display: inline-block; float: none; }
.color .evo-pointer { vertical-align: middle; display: inline-block; width: 24px; height: 24px; float: none; margin-left: 8px; border-radius: 4px; }
.color .colorPicker,
.color .evo-pointer { *display: inline/*IE6,7*/;
}
/* Scrollbar jQuery Plugin
-------------------------------------- */
.ps-container .ps-scrollbar-x,
.ps-container .ps-scrollbar-y { background-color: #AAA; height: 8px; -webkit-border-radius: 4px; -moz-border-radius: 4px; border-radius: 4px; position: absolute; z-index: auto; bottom: 3px; opacity: 0; filter: alpha(opacity=0); -webkit-transition: opacity.25s linear; -moz-transition: opacity .25s linear; transition: opacity .25s linear; }
.ps-container .ps-scrollbar-y { right: 3px; width: 8px; bottom: auto; }
.ps-container:hover .ps-scrollbar-x,
.ps-container:hover .ps-scrollbar-y { opacity: .6; filter: alpha(opacity=60); }
.ps-container .ps-scrollbar-x:hover,
.ps-container .ps-scrollbar-y:hover { opacity: .9; filter: alpha(opacity=90); cursor: default; }
.ps-container .ps-scrollbar-x.in-scrolling,
.ps-container .ps-scrollbar-y.in-scrolling { opacity: .9; filter: alpha(opacity=90); }
/*商家入驻表单*/
table.type { width: 700px; border: solid 1px #EEE; }
table.type thead th { color: #555; background-color: #F7F7F7; text-align: center; padding: 4px; border-color: #EEE; }
table.type tbody td { color: #777; text-align: center; padding: 4px; border-color: #EEE; }
table.type tbody td input { width: 60px; padding: 0; }
/* 翻页样式 */
.pagination { text-align: center; display: block; padding: 15px 0; margin: 0!important; }
.pagination ul { font-size: 0; *word-spacing:-1px/*IE6、7*/;
display: inline-block; *display: inline/*IE6,7*/;
margin: 0 auto!important; padding: 0; zoom: 1; }
.pagination ul li { vertical-align: middle; letter-spacing: normal; word-spacing: normal; display: inline-block; width: auto!important; height: auto!important; padding: 0!important; margin: 0 !important; border: none !important; }
.pagination ul li {
*display: inline/*IE6,7*/;
*float: left; zoom: 1; }
.pagination li span { font-size: 12px; line-height: 24px; color: #AAA; list-style-type: none; background-color: #F5F5F5; display: block; height: 24px; padding: 0px 8px; margin: 0px; border: 1px solid; border-color: #DCDCDC #DCDCDC #B8B8B8 #DCDCDC; }
.pagination li:first-child span { border-radius: 4px 0 0 4px; }
.pagination li:last-child span { border-radius: 0 4px 4px 0; }
.pagination li a span,
.pagination li a:visited span { color: #333; text-decoration: none; cursor: pointer; }
.pagination li a:hover { text-decoration: none; }
.pagination li a:hover span,
.pagination li a:active span { color: #333; background-color: #E8E8E8; border-color: #D0D0D0 #D0D0D0 #AEAEAE #D0D0D0; cursor: pointer; }
.pagination li span.currentpage { color: #FFF; font-weight: bold; background-color: #41BEDD; border-color: #3AABC6 #3AABC6 #318EA6 #3AABC6; border-radius: 0; }
.paixv a { text-decoration: none; color: #000 }
.paixv a:hover { color: #06F }
.hDiv::-webkit-scrollbar{display:none;}
span.err{color:#F00; display:none;}

.eylogo {
    padding-top:18px;
}
.eylogo a {
    height: 60px;
    line-height: 60px;
    padding-left: 25px;
    text-align: left;
}
.eylogo a img {
    height: 50px;
    vertical-align: middle;
}
.eycms_cont_left{ float:left; width:200px; position:relative; background:#3398cc; color:#fff; z-index:99;height:100%;font-size:14px; }
.eycms_cont_left a{ color:#fff;font-family: 微软雅黑;}
.eycms_cont_left dl{ padding-bottom:18px; position:relative;}
.eycms_cont_left dl dt{ padding:15px 25px 5px; color:#e7e7e7;}
.eycms_cont_left dl dd a{ display:block; overflow: hidden; outline:none; padding-left:20px; height:40px; line-height:40px;transition:all .3s;-webkit-transition:all .3s;-moz-transition:all .3s;-o-transition:all .3s  }
.eycms_cont_left dl dd a i,.eycms_cont_left dl.jslist dt i{ margin-right:8px; width:28px; text-align:center; color:#fff; position:relative; top:1px; font-size:16px; }
.eycms_cont_left dl dd a:hover,.eycms_cont_left dl dd a.on,.eycms_cont_left dl dt.on{ background:#2189be;  }
.eycms_cont_left dl.on{}
.eycms_cont_left dl dd a:active{ background:#2189be; }
.eycms_cont_left dl.jslist{ padding-bottom:0px;transition:all .3s;-webkit-transition:all .3s;-moz-transition:all .3s;-o-transition:all .3s}
.eycms_cont_left dl.jslist dt{ padding:0px; padding-left:20px; height:40px; line-height:40px; color:#fff; cursor:pointer; position:relative;overflow: hidden; }
.eycms_cont_left dl.jslist dt a{ display:block; }
.eycms_cont_left dl.jslist dd{ display:none; position:absolute; left:200px; bottom: 0px; background:#3398cc; width:200px;}
.eycms_cont_left dl:nth-child(2) dd dl.jslist dd{ top: 0px !important; bottom: auto !important; }
.eycms_cont_left dl.jslist dt i.fa-angle-right{ position:absolute; font-size:20px; color:#b5d7f1; right:0px;  height:40px; line-height:40px; }
.eycms_cont_left dl.jslist dd a:hover,.eycms_cont_left dl.jslist dd a.on{ background:#2189be;}
.eycms_cont_left dl.jslist:hover{ background:#2189be;}
.eycms_cont_left dl.jslist:hover dd{ display:block !important}
i.fa-mobile{ font-size:26px!important; top:4px!important;}
.eycms_cont_left dl i.fa-plus{ color:#71fdff!important;}
/*.eycms_cont_left dl i.fa-cube{ color:#ff80ad!important;}*/

.fixedbar { zoom: 1;text-align: center; }
.fixedbar .fixedbar-box{padding:10px 0px 10px 20px;text-align: left;}
.fixedbar .fixedbar-box .left{position: relative;
    left: -2.5px;color: #a7c6dc;}
.fixedbar .fixedbar-box .right{position: relative;
    left: 2.5px;color: #a7c6dc;}
.fixedbar:after { content: " "; display: block; clear: both; height: 0; }
.fixedbar a { display: inline-block;  padding:0px 5px; color: #a7c6dc; text-align: center;  transition: all .23s ease-out; }
.fixedbar a .fa { display: block; font-size: 20px; }
.fixedbar a span { font-size: 14px; }
.fixedbar a:hover {color: #eee; }
.fold .hidden-xs{ display: none; }

 .ey-tool .dropdown-toggle {
    border: 0px;
    cursor: pointer;
        margin-right: 18px;
    font-size: 14px;    color: #333;
}
.ey-tool i{margin-right:2px;}
.ey-tool i.fa-globe{font-size:18px;position:relative;top:2px;}
.ey-tool i.fa-bell-o{font-size:17px;position:relative;top:1px;left:3px;}
.ey-msecount-tool span.label{position:absolute;right:8px;top:5px;border-radius:59px;height:16px;line-height:16px;font-size:12px;padding:0px 5px;font-weight:normal;}
.ey-tool li.ey-tool-list{margin:5px 8px;color:#666;}
select{ box-shadow: none; marign:0px !important; }
.c_art_title div{ white-space: normal !important; }
.item-title a.back i { font-size: 28px; }
.ncap-form-default dd.opt a.ui_tips{background: url(../images/ui_tip.png) no-repeat; opacity: 0.5; background-size: 100%; display: inline-block; text-indent: -999px; width: 15px; height: 21px; vertical-align: bottom; margin-left: 3px; }
.checkboxall{ height: 28px; line-height: 44px; }
.ui-text .ui_tips,.ui-keyword .ui_tips,.zzbaidu .ui_tips  { display: none !important;; }
.ui-text .notic,.ui-keyword .notic,.zzbaidu .notic{ display: block !important; }

.ui-web_name .ui_tips,.ui-web_title .ui_tips,.ui-keyword .ui_tips,.ui-web_description .ui_tips  { display: inline-block !important; }

.dropdown-menu {
    position: absolute;
    top: 26px;
    right: 16px;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
}
.dropdown-menu>li>a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
    text-align: center;
}
.dropdown-menu>li>a:focus,.dropdown-menu>li>a:hover{color:#262626;text-decoration:none;background-color:#f5f5f5}
em.open{ position: relative; }
em.open>.dropdown-toggle.btn-default {
    color: #333;
    background-color: #e6e6e6;
    border-color: #adadad;
}

em.open .dropdown-toggle {
    -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
    box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
}

.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
}

em.open .dropdown-menu{ display: block; }
.btn-primary {
    color: #fff;
    background-color: #3398cc;
    width: 100%;
}


.w40x {
    width: 20px;
    height: 18px;
    float: left;
    position: relative;
    margin-right: 5px;
}

.w40x:before {
    content: '';
    position: absolute;
    top: -15px;
    left: 50%;
    width: 1px;
    height: 24px;
    background: #ccc;
}

.w40x:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 50%;
    height: 1px;
    background: #ccc;
}
.w40xc{ margin-right: 25px }
.w40xc:after{ width: 150%;  } 

img.layer_tips_img {
    width: 150px;
    max-height: 250px;
}

.flexigrid .mDiv .fbutton div.add{padding: 2px 16px;color: #fff;background-color: #FF6666;border-color: #FF6666; border-radius: 3px;}
.flexigrid .mDiv .fbutton div.add:hover{ background-color: #FF534D;border-color: #FF534D;}
.flexigrid .mDiv .fbutton{ float: right; padding-right: 0px; margin-right: 0px;}
div.addartbtn{ border:none !important; }
.addartbtn input.btn{ color: #fff;background-color: #FF6666;border:1px solid #FF6666 !important; height: 28px !important;} 
.addartbtn input.btn:hover{background-color: #FF534D !important;}
.addartbtn input.btn.current{background: #e7e7e7;color: #000; border:none !important;}
.addartbtn input.btn.current:hover{ background: #FF6666 !important; color:#fff; /* cursor: default; */ }
div.probtn{ float: left; margin-right: 6px; }
div.probtn input.btn{ color: #fff;background-color: #25C6FC; border:none !important;border:1px solid #25C6FC !important;}
div.probtn input.btn:hover{background-color: #3f6fb6 !important;}

a.upimg{}
a.upimg:hover{ cursor: move;}

.item-title .subject i{ float: left; font-size: 24px; color: #999; margin-right: 6px; }

.atta .ncap-form-default dt.tit{ width: 134px; padding-left: 0px; }

.other .flexigrid .bDiv td div img{ float: none; }
.flexigrid .bDiv td div i.arctotal{font-size: 12px;font-family: 宋体;color: #999;}
.flexigrid .bDiv td div.pb0{ padding-bottom: 0px; }

.form-horizontal .ncap-form-default div.bot{ padding-bottom: 128px; padding-top: 0px;}
.form-horizontal .ncap-form-default dl.row{ border-bottom: none; }
/*基本信息-开发模式*/
.system-web{background-color: #FFF; overflow: auto;}
.system-web .ncap-form-default dd.opt{width: 60%;}
.system-web .variable{ float: right; width: 200px; }
.system-web .variable div {width: 60%; float: left; height: 28px; font-size: 12px; text-align:center; line-height: 28px; }
.system-web .variable div p{border-right: 1px solid #eee;}
.system-web .variable div.r{ width: 40% }

/*a  upload 上传input美化 */
.a-upload {
    padding: 4px 10px;
    height: 20px;
    line-height: 20px;
    position: relative;
    cursor: pointer;
    color: #333;
    background: #fafafa;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    font-size:13px;
}

.a-upload  input {
    position: absolute;
    font-size: 100px;
    right: 0;
    top: 0;
    opacity: 0;
    filter: alpha(opacity=0);
    cursor: pointer
}

.a-upload:hover {
    color: #444;
    background: #eee;
    border-color: #ccc;
    text-decoration: none
}

/*图集上传 bug修正 2019.4.25*/
.ui-sortable-placeholder{ display: inline-block; }
.tab-pane .images_upload {
    float: left;
    width: 19%;
    margin-right: 1%;
    margin-top: 15px;
    display: inline-block;
}

.tab-pane .images_upload .ic{
    border: 1px solid #ddd;
    padding: 5px;
    border-radius: 4px;
    position: relative;
    float: left;
    background: #fff;
}
.tab-pane .images_upload .cover-bg{ background: #000; opacity: 0.5; width:100%; height: 100%; position: absolute; z-index: 2 }
.tab-pane .images_upload .icaction{position: absolute; top:50%; margin-top: -15px; text-align: center; width:100%; z-index: 111}
.tab-pane .images_upload .icaction span{ height: 28px; line-height: 28px; padding: 0 8px; background: #FF6666;  color: #fff; display: inline-block; cursor: pointer;}

.tab-pane .images_upload .upimg img{     
    vertical-align: middle;
    max-width: 100%;
    max-height: 140px;
}
.tab-pane .images_upload .ic img{
    position:absolute;
    top:50%;
    transform:translate(-50%,-50%);
    z-index: 1;
    left:50%;
}
.tab-pane .images_upload textarea{ 
    width:100%; 
    box-sizing: 
    border-box;
    margin-top: 5px;
    font-size: 14px;
    height: 50px;
    float: left;
    color: #999;
    border-color: #ddd;
    border-radius: 3px;
    -webkit-appearance: none;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.065) inset;
    transition: all 1s cubic-bezier(0.175, 0.885, 0.32, 1) 0s;
}
.tab-pane .table-bordered{ width:100%; }
.tab-pane a.upimg,.tab-pane div.upimg{ 
    height: 140px;
    background: #fafafa;
    overflow: hidden;
    text-align: center;
    position: relative;
    display: block;
}

.tab-pane .operation a{
    text-align: center;
    color: #666;
    outline: none;
    width: 33.3333%;
    float: left;
    font-size: 14px;

}
.tab-pane .operation a input{ display: inline-block; margin-right: 3px; vertical-align:middle; margin-top: -3px;}
a.imgupload{
    display: inline-block;
    padding: .5em 1em;
    vertical-align: middle;
    font-size: 1.0rem;
    font-weight: 400;
    line-height: 1.2;
    text-align: center;
    white-space: nowrap;
    border-radius: 0;
    cursor: pointer;
    outline: 0;
    -webkit-transition: background-color .3s ease-out,border-color .3s ease-out;
    transition: background-color .3s ease-out,border-color .3s ease-out;
    color: #fff;
    background-color: #3bb4f2;
    border-color: #3bb4f2;
}
.tab-pane .operation a label{
     cursor: pointer;
}
a.imgupload i{font-size: 1.0rem;}
a.imgupload:hover{ background-image: linear-gradient(160deg, #3bb4f2 20%,#008cff 80%);}

 @media (max-width:1680px) {
.tab-pane .images_upload{width: 24%;}
.tab-pane .operation a{ font-size: 12px; }
.tab-pane .operation a label{cursor: pointer;}
}
div.tab-pane.pics a.upimg{ height: auto; padding: 8px; padding-bottom:0px; }
div.tab-pane.pics .images_upload{ width:auto; }
div.tab-pane.pics .delect{ text-align: center; display: block; background: #fafafa }

.template_div{margin: 5px 0px;}
