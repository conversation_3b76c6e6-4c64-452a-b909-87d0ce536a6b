@charset "utf-8";
* { word-wrap: break-word; outline: none; }
body { background: #FFF; min-width: 1000px; }
html, body { height: 100%; overflow: hidden; }
html { -webkit-text-size-adjust: none; }
body, td, input, textarea, select,
button { color: #555; font-size: 13px; font-family: "Microsoft Yahei", "Lucida Grande", Verdana, Lucida, Helvetica, Arial, sans-serif; }
body, ul, ol, li, dl, dt, dd, p, h1, h2, h3, h4, h5, h6, form, fieldset { margin: 0; padding: 0; }
ul, ol, li { list-style-image: none; list-style-type: none; }
h1, h2, h3, h4, h5, h6 { font-size: 12px; }
a { color: #3b639f; text-decoration: none; blr:expression(this.onFocus=this.blur()) }
a:hover { color: #CD0200; text-decoration: none; }
a img { border: none; }
em, cite, th { font-style: normal; font-weight: normal; }
table { border-collapse: collapse;word-break:break-all;word-wrap:break-word; }
th { text-align: left; }
input, button, select, textarea { outline: none; margin: 0px; padding:0px;}
i.fa { font-size: 14px; vertical-align: baseline; margin-right: 4px; }/*字体图标属性*/
i.iconfont { font-size: 14px; vertical-align: baseline;}/*字体图标属性*/
.tl { text-align: left; }
.tc { text-align: center; }
.tr { text-align: right; } 
.text-l10{ text-indent: 10px;}
.pdl10 { padding-left: 10px !important;}
.pdl15 { padding-left: 15px !important;}
.nobg { background-color: transparent !important; background-image: none !important; }
.nopd { padding: 0!important; }
.nobd { border-width: 0!important; border-color: transparent!important; border-style: none!important; border-radius: 0!important; }
.nobs, .nobs:hover { box-shadow: none!important; }
.no-drop{ cursor: no-drop !important; }
.none {display: none;}
.blocki{ display:inline-block !important; }
.curpoin{ cursor: pointer !important; }
i,em{ font-style: normal; }
/*body样式 2019-06-13 浩 */
.bodystyle{height: 93%; background-color: #F4F4F4; padding: 15px;overflow:auto;}
.bodysy-w{background: #F5F5F5; overflow: auto; cursor: default; -moz-user-select: inherit;min-width:auto;}
/*兼容html5
******************************/
article, aside, dialog, footer, header, section, footer, nav, figure, menu { display: block; }

/*长度高度
******************************/
.w0 {width: auto !important; }
.w10 { width: 10px; }
.w20 { width: 20px; }
.w30 { width: 30px; }
.w40 { width: 40px !important; }
.w50 { width: 50px !important; }
.w60 { width: 60px !important; }
.w65 { width: 65px !important; }
.w70 { width: 70px !important; }
.w80 { width: 80px !important; }
.w90 { width: 90px; }
.w100 { width: 100px !important; }
.w110 { width: 110px !important; }
.w120 { width: 120px !important; }
.w130 { width: 130px !important; }
.w140 { width: 140px !important; }
.w150 { width: 150px !important; }
.w160 { width: 160px; }
.w180 { width: 180px; }
.w190 { width: 190px !important; }
.w200 { width: 200px !important; }
.w210 { width: 210px !important; }
.w220 { width: 220px !important; }
.w230 { width: 230px; }
.w240 { width: 240px; }
.w250 { width: 250px !important; }
.w270 { width: 270px; }
.w280 { width: 280px !important; }
.w300 { width: 300px !important; }
.w340 { width: 340px; }
.w350 { width: 350px !important; }
.w400 { width: 400px!important; }
.w450 { width: 450px!important; }
.w500 { width: 500px; }
.w600 { width: 600px !important; }
.w700 { width: 700px; }
.w780 { width: 780px; }
.w800 { width: 800px; }

.h10{ height: 10px;}
.h12{ height: 12px;}
.h15{ height: 15px;}
.h20{ height: 20px;}
.h30{ height: 30px;}
.h40{ height: 40px;}
.h50{ height: 50px;}

.lh30{ line-height:30px!important;}
.lh40{ line-height:40px!important;}
/*边距
******************************/
.m0 { margin: 0!important; }
.m10 { margin: 10px; }
.m15 { margin: 15px !important; }
.m30 { margin: 30px; }
.mt5 { margin-top: 5px; }
.mt10 { margin-top: 10px !important; }
.mt12 { margin-top: 12px !important; }
.mt15 { margin-top: 15px; }
.mt20 { margin-top: 20px !important; }
.mt30 { margin-top: 30px !important; }
.mt50 { margin-top: 50px !important; }
.mt100 { margin-top: 100px !important; }
.mb5 { margin-bottom: 5px !important; }
.mb10 { margin-bottom: 10px !important; }
.mb15 { margin-bottom: 15px; }
.mb20 { margin-bottom: 20px; }
.mb30 { margin-bottom: 30px !important; }
.mb50 { margin-bottom: 50px; }
.mb100 { margin-bottom: 100px; }
.ml5 { margin-left: 5px!important; }
.ml10 { margin-left: 10px!important; }
.ml15 { margin-left: 15px; }
.ml20 { margin-left: 20px; }
.ml30 { margin-left: 30px; }
.ml50 { margin-left: 50px; }
.ml100 { margin-left: 100px !important; }
.ml200 { margin-left: 200px !important; }
.mr5 { margin-right: 5px !important; }
.mr10 { margin-right: 10px !important; }
.mr15 { margin-right: 15px !important; }
.mr20 { margin-right: 20px; }
.mr30 { margin-right: 30px !important; }
.mr50 { margin-right: 50px !important; }
.mr100 { margin-right: 100px; }
.mb-20 { margin-bottom: -20px !important; }
/*边距
******************************/
.p10 { padding: 0px; }
.p10 { padding: 10px; }
.p15 { padding: 15px; }
.p30 { padding: 30px; }
.pt5 { padding-top: 5px; }
.pb10 { padding-bottom: 10px !important; }
.pt15 { padding-top: 15px; }
.pt20 { padding-top: 20px; }
.pt30 { padding-top: 30px; }
.pt50 { padding-top: 50px; }
.pt100 { padding-top: 100px; }
.pb5 { padding-bottom: 5px; }
.pb10 { padding-bottom: 10px; }
.pb15 { padding-bottom: 15px; }
.pb20 { padding-bottom: 20px !important; }
.pb30 { padding-bottom: 30px; }
.pb50 { padding-bottom: 50px; }
.pb100 { padding-bottom: 100px; }
.pl0 { padding-left: 0px!important; }
.pl5 { padding-left: 5px!important; }
.pl10 { padding-left: 10px!important; }
.pl15 { padding-left: 15px!important; }
.pl20 { padding-left: 20px; }
.pl30 { padding-left: 30px; }
.pl50 { padding-left: 50px; }
.pl100 { padding-left: 100px; }
.pl120 { padding-left: 120px!important; }
.pr5 { padding-right: 5px; }
.pr10 { padding-right: 10px; }
.pr15 { padding-right: 15px; }
.pr20 { padding-right: 20px; }
.pr30 { padding-right: 30px; }
.pr50 { padding-right: 50px; }
.pr100 { padding-right: 100px; }
.pad-tb12 {padding: 12px 0 !important;}
.fl{ float: left; }
.fr{ float: right; }



/* 最小屏高 */
.min-hg-c { min-height: calc(100% - 102px);}
/* 文本垂直居中 */
.vertical-center{
    align-items:center;
    display: -webkit-flex;
}
/* 文本水平居中 */
.horizontally{
    justify-content:center;
    display: -webkit-flex;
}
/* 文本过长显示省略号 */
.ellipsis{
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
}
.d-flex{ display: flex !important;}
.d-grid{ display: grid !important;;}
.l-height-24{ line-height: 24px;}
input[readonly="readonly"]:hover{ cursor: no-drop !important;}
/* 返回顶部、底部控制 */
#goTop { background-color: #FFF; border-radius: 4px; width: 32px; height: 64px; border: solid 1px #E6E6E6; position: fixed; z-index: 99; bottom: -166px; right: 10px; overflow: hidden; opacity: 0.5; box-shadow: 0 0 10px 0 rgba(153,153,153,0.25); }
#goTop:hover { opacity: 1; box-shadow: 0 0 0 2px rgba(153,153,153,0.075); }
#goTop a { color: #CCC; text-align: center; display: block; width: 32px; height: 32px; margin-top: -1px; border-top: dashed 1px #E6E6E6; cursor: pointer; }
#goTop a i { font-size: 28px; line-height: 32px; margin: 0; }
#goTop a:hover { color: #1bbc9d; }
/*debug*/
.trace { font-family: Verdana, Geneva, sans-serif; font-size: 12px; background: #FAFAFA; margin: 6px; border: 1px dashed #E7E7E7; padding: 8px; }
.trace fieldset { margin: 5px; }
.trace fieldset legend { color: #333; font-weight: bold }
.trace fieldset div { line-height: 16px; padding: 10px; overflow: auto; height: 300px; text-align: left; }
.nc-row { font-size: 0!important; *word-spacing:-1px/*IE6、7*/;}
.nc-row li { font-size: 12px; vertical-align: top; letter-spacing: normal; display: inline-block!important; *display: inline/*IE7*/; *zoom:1/*IE7*/;}
.clear { font-size: 0; line-height: 0; height: 0; clear: both; float: none; padding: 0; margin: 0; border: 0; zoom: 1; }

/* ==========================
 * 定义部分jquery ui样式
 * ========================== */
/*title文字提示*/
.ui-tooltip { color: #FFF !important; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#E5333333', endColorstr='#E5333333')!important;
background: rgba(51,51,51,0.9) !important; border: 0!important; box-shadow: 0 0 5px rgba(0,0,0,0.25)!important; }
/*用于picTip.js配合图片显示
******************************/
.trans_msg { background-color: #FFF; padding: 4px; border: solid 1px #CCC; box-shadow: 0 0 0 2px rgba(153,153,153,0.1); filter: alpha(opacity=100, enabled=1) revealTrans(duration=.2, transition=1) blendtrans(duration=.2); }
.trans_msg img { display: block; max-width: 150px; max-height: 150px; }
.warp-all { width: 1000px; margin: 0 auto; }
/*dialog弹出框*/
.dialog_wrapper { padding: 0 !important; border-radius: 0 !important; box-shadow: 0 0 10px rgba(0,0,0,0.25) !important; }
.dialog_body { border: solid 1px #C8C8C8!important; }
.dialog_head { border-bottom: none!important; }
.dialog_title { height: 24px!important; }
.dialog_title_icon { font-size: 16px!important; font-weight: normal!important; line-height: 24px!important; color: #333!important; }
.dialog_close_button { font-size: 12px!important; line-height: 20px!important; color: #999!important; background-color: #F5F5F5!important; text-align: center!important; width: 20px!important; height: 20px!important; border: none!important; border-radius: 50%!important; position: absolute!important; right: 10px!important; top: 8px!important; }
.dialog_close_button:hover { color: #FFF!important; background-color: #F33!important; }
.highcharts-container { margin-top: 20px !important; border: solid 1px #D7D7D7!important; box-shadow: 0 0 0 2px rgba(204,204,204,0.25); }
.highcharts-container .highcharts-title { display: block!important; width: 100% !important; }
.highcharts-container .highcharts-title tspan { font-size: 16px !important; font-weight: normal !important; position: absolute!important; z-index: 1!important; top: 0!important; left: 0!important; }
/*表单元素样式*/
input[type="text"],
input[type="password"],
textarea,
select,
.editable,
.editable-tarea { color: #555555; background-color : #FFF; border: solid 1px #eee; }
input[type="text"],
input[type="password"],
textarea,
select,
.editable,
.editable2,
.editable-tarea,
.editable-tarea2 { padding: 4px 6px; /*Firefox\Chrome\Safari\IE9\元素圆角效果*/ resize: none;/*禁止调节元素的尺寸*/ }
input[type="text"]:focus,
input[type="text"]:hover,
input[type="text"]:active,
input[type="password"]:focus,
input[type="password"]:hover,
input[type="password"]:active,
textarea:hover,
textarea:focus,
textarea:active { color: #33464F; background-color: #fff; border: 1px solid; border-color:rgba(82,168,236,0.8); -moz-box-shadow: 0 0 0 2px rgba(82, 168, 236, 0.15); -webkit-box-shadow: 0 0 0 0 2px rgba(82, 168, 236, 0.15); box-shadow: 0 0 0 2px rgba(82, 168, 236, 0.15); outline: 0 none; }
input[disabled="disabled"],
input[readonly="readonly"],
input[disabled="disabled"]:hover,
input[readonly="readonly"]:hover,
input[disabled="disabled"]:focus,
input[readonly="readonly"]:focus { background: #F5F5F5; border-color: #D7D7D7; box-shadow: none; }
.editable2,
.editable-tarea2 { color: #33464F; background-color: #FFF; border: 1px dotted; border-color: #71CBEF; }
textarea { resize: vertical !important;/*Textarea支持调节元素的高度*/ }
.editable,
.editable2,
input[type="text"],
input[type="password"] { line-height: 20px; white-space: nowrap; display: inline-block; height: 20px; overflow: hidden; cursor: text; font-size: 13px;}
.editable-tarea,
.editable-tarea2,
textarea { line-height: 18px; display: inline-block; height: 36px; cursor: text; overflow: auto;}
.tarea { height: 75px; width: 400px; }
.txt,
select,
.vmiddle { vertical-align: middle; }
.sort input,
.sort .editable,
.sort .editable2 { width: 30px; margin-top: -5px; text-align: center; border:none;}
.name input,
.name .editable,
.name .editable2 { width: 250px; }
.tag input,
.tag .editable,
.tag .editable2 { width: 480px; }
.goods-name textarea,
.editable-tarea,
.editable-tarea2 { width: 250px; }
.class input,
.class .editable,
.class .editable2 { width: 120px; }
input.readonly,
textarea.readonly,
textarea.readonly:focus,
textarea.readonly:hover,
input.readonly:focus,
input.readonly:hover { backgorund: #FFF; border: solid 1px; border-color: #EEE #F5F5F5 #F5F5F5 #EEE; }
input::-webkit-input-placeholder, textarea::-webkit-input-placeholder { /* WebKit, Blink, Edge */    color: #9e9e9e; }
input:-moz-placeholder, textarea:-moz-placeholder { /* Mozilla Firefox 4 to 18 */   color: #9e9e9e; }
input::-moz-placeholder, textarea::-moz-placeholder { /* Mozilla Firefox 19+ */   color: #9e9e9e; }
input:-ms-input-placeholder, textarea:-ms-input-placeholder { /* Internet Explorer 10-11 */   color: #9e9e9e; }
.checkbox { display: inline-block; vertical-align: middle; margin-right: 4px; zoom: 1; }
.input-file-show { background-color: #FFF; vertical-align:middle; display: inline-block; padding-left: 26px; border: solid 1px #D7D7D7; position: relative; z-index: 1; }
.input-file-show:hover { border-color: #00B7EE; -moz-box-shadow: 0 0 5px rgba(44, 188, 163, 0.5); -webkit-box-shadow: 0 0 5px rgba(44, 188, 163, 0.5); box-shadow: 0 0 5px rgba(44, 188, 163, 0.5); }
.input-file-show span.show { width: 22px; height: 24px; text-align: center; padding: 2px; position: absolute; z-index: 2; top: 0; left: 0; }
.input-file-show span.show a { color: #AAA; display: block; width: 22px; height: 22px; }
.input-file-show:hover span.show a,
.input-file-show span.show a:hover { color: #00B7EE; text-decoration: none; }
.input-file-show span.show i { font-size: 14px; line-height: 24px; display: block; }
.input-file-show span.show img { max-width: 22px; max-height: 24px; }
.type-file-box { display: block; width: 360px; height: 28px; position: relative; z-index: 1; }
.type-file-text { line-height: 26px !important; display: block; width: 261px; height: 26px !important; float: left !important; padding: 0 !important; margin: 0 !important; border: none 0 !important; border-radius: 0 !important; }
.type-file-button,
.type-file-button:focus { background-color: #E6E6E6; display: block; width: 99px; height: 28px; float: left !important; border: 0;  }
.input-file-show:hover .type-file-button { color: #FFF; background-color: #00B7EE; }
.type-file-file { width: 96px; height: 28px; position: absolute; top: 0; right: 0; filter:alpha(opacity:0);
opacity: 0; cursor: pointer; }
.type-file-preview { background: #FFF; display: none; padding: 5px; border: solid 5px #71CBEF; position: absolute; z-index: 999; }
.image_display .type-file-show { width: 16px; height: 16px; padding: 2px; border: solid 1px #D8D8D8; cursor: auto; }
/*按钮*/
a.ncap-btn-mini { font: normal 12px/20px arial; color: #777; background-color: #F5F5F5; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 0 10px; margin-right: 2px; border: solid 1px #DCDCDC; cursor: pointer; }
a.ncap-btn-dis { font: normal 12px/20px "microsoft yahei"; text-decoration: none; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 2px 9px; border: solid 1px; border-radius: 3px;background-color:#c5c5c5;color:rgb(119, 119, 119);border-color:#c5c5c5; cursor: auto;text-decoration: none;}
a:hover.ncap-btn-mini { text-decoration: none; color: #333; background-color: #E6E6E6; border-color: #CFCFCF #CFCFCF #B3B3B3 #CFCFCF; }
a.ncap-btn { font: normal 12px/20px "microsoft yahei"; text-decoration: none; color: #777; background-color: #F5F5F5; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 2px 9px; border: solid 1px; border-color: #DCDCDC #DCDCDC #B3B3B3 #DCDCDC; border-radius: 3px; cursor: pointer; }
a:hover.ncap-btn { text-decoration: none; color: #333; background-color: #E6E6E6; border-color: #CFCFCF #CFCFCF #B3B3B3 #CFCFCF; }
a.ncap-btn-big { font: bold 14px/20px "microsoft yahei", arial; color: #777; background-color: #ECF0F1; text-align: center; vertical-align: middle; display: inline-block; height: 20px; padding: 7px 19px; border: solid 1px #BEC3C7; border-radius: 0px; cursor: pointer; }
a:hover.ncap-btn-big { text-decoration: none; color: #FFF; background-color: #BEC3C7; box-shadow: 0 0 0 2px rgba(82, 168, 236, 0.15); }
a.ncap-btn-mini i,
a.ncap-btn i,
a.ncap-btn-big i { font-size: 14px; vertical-align: baseline; margin-right: 4px; }
a.ncap-btn-blue,
a.ncap-btn-acidblue,
a.ncap-btn-green,
a.ncap-btn-orange,
a.ncap-btn-red,
a.ncap-btn-black,
a:hover.ncap-btn-blue,
a:hover.ncap-btn-acidblue,
a:hover.ncap-btn-green,
a:hover.ncap-btn-orange,
a:hover.ncap-btn-red,
a:hover.ncap-btn-black,
.nscs-table-handle a.btn-orange-current { color: #FFF; text-shadow: 0 -1px 0 rgba(0,0,0,0.10); }
a.ncap-btn-blue { background-color: #3598DC; border-color: #2A80B9; }
a.ncap-btn-acidblue,
.nscs-table-handle a:hover.btn-acidblue { background-color: #49AFCD; border-color: #429DB8 #429DB8 #37839A #429DB8; }
a.ncap-btn-green { background-color: #4fc0e8; border: none; font-weight: normal;border-radius: 0px;}
a.ncap-btn-no { background-color: #eee; color: #999; border: none; font-weight: normal;border-radius: 0px;}
a.ncap-btn-no:hover { text-decoration: none;color: #999;background-color: #eaeaea;box-shadow: none;}
a.ncap-btn-orange,
.nscs-table-handle a:hover.btn-orange,
.nscs-table-handle a.btn-orange-current { background-color: #FAA732; margin: 0; border-style: solid; border-width: 1px; border-color: #E1962D #E1962D #BB7D25 #E1962D !important; }
a.ncap-btn-red,
.nscs-table-handle a:hover.btn-red { background-color: #DA4F49; border-color: #C44742 #C44742 #A33B37 #C44742; }
a.ncap-btn-black,
.nscs-table-handle a:hover.btn-black { background-color: #363636; border-color: #313131 #313131 #282828 #313131; }
a:hover.ncap-btn-blue { background-color: #2A80B9; }
a:hover.ncap-btn-acidblue { background-color: #2F96B4; border-color: #2A87A2 #2A87A2 #237087 #2A87A2; }
a:hover.ncap-btn-green { background-color: #3aa8cf; }
a:hover.ncap-btn-orange { background-color: #F89406; border-color: #DF8505 #DF8505 #BA6F04 #DF8505; }
a:hover.ncap-btn-red { background-color: #BD362F; border-color: #AA312A #AA312A #8E2823 #AA312A; }
a:hover.ncap-btn-black { background-color: #222222; border-color: #1F1F1F #1F1F1F #191919 #1F1F1F; }
a.ncap-btn2 {
    font: normal 12px/20px "microsoft yahei"; 
    text-decoration: none; 
    color: #777; 
    background-color: #F5F5F5; 
    text-align: center; 
    vertical-align: middle; 
    display: inline-block; 
    height: 20px; 
    padding: 2px 9px; 
    border: 1px solid #a5a3a3;
    border-color: #DCDCDC #DCDCDC #B3B3B3 #DCDCDC; 
    border-radius: 3px; 
    cursor: pointer;
}
a.ncap-btn2:hover {
    background-color: #eae5e5;
    color: #333;
}
/*上传按钮*/
.ncap-upload-btn { vertical-align: top; display: inline-block; *display: inline/*IE7*/;
margin-right: 10px; *zoom:1;
position: relative; z-index: 1; }
.ncap-upload-btn .input-file,
.ncap-upload-btn .input-button { width: 84px; height: 26px; position: absolute; z-index: 2; top: 0; left: 0; right: 0; bottom: 0; opacity: 0; filter: alpha(opacity=0); cursor: pointer; }
.ncap-upload-btn .input-button { z-index: 1; }
/*格式化jquery-ui*/
#stat_tabs.ui-corner-all { border-radius: 0!important; margin-top: 10px; }
#stat_tabs.ui-widget-content,
#stat_tabs .ui-widget-content { background: transparent none!important; border: none 0!important; }
#stat_tabs .ui-tabs,
#stat_tabs .ui-tabs .ui-tabs-panel,
#stat_tabs .ui-tabs .ui-tabs-nav { padding: 0!important; }
#stat_tabs .ui-state-default { border: none 0!important; float: none!important; }
#stat_tabs .ui-tabs-active .ui-tabs-anchor { font-size: 14px!important; font-weight: 600!important; color: #777 !important; background-color: #fff !important; border-bottom-color: #fff!important; }
.ui-widget-header { background: transparent none!important; border: none 0!important;/*弹出框标题底色去除*/ }
#ui-datepicker-div { z-index: 99!important;/*日期控件*/ }
.area-region-select select { margin-right: 5px;}
.area-region-select .input-btn { margin-left: 10px;}
/* ==========================
 * 管理平台布局内容
 * ========================== */

/* 头部
******************************/
.admincp-header { width: 100%; position: relative; z-index: 2; height:51px; background-color: #fff;        box-shadow: 0 2px 4px rgba(0,0,0,.08);}
.admincp-header .wraper{ height:48px;}
.admincp-name { width: auto; padding: 10px 0 6px 70px; float: left; text-shadow: 0 1px 3px rgba(0,0,0,0.25); }
.admincp-name em{font: normal 27px "Microsoft Yahei";line-height: 27px;color: #fff;}
.admincp-name h1 { font-family: Verdana, Geneva, sans-serif; font-size: 14px; line-height: 20px; font-weight: 700; color: #FFF; }
.admincp-name h2 { font-size: 12px; line-height: 16px; color: #FFF; }
.nc-module-menu { float: left; overflow: hidden; }
.nc-module-menu ul { }
.nc-module-menu ul li { line-height: 38px; height: 38px;}
.nc-module-menu ul li a { font-size: 15px; line-height: 38px;  display: block; opacity: 0.8;  width:100%; text-align:center; color: #FFF;}
.nc-module-menu ul li.active a {
filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#26000000', endColorstr='#26000000'); background: rgba(0,0,0,0.15); opacity: 1;color:#ff8800  }
.nc-module-menu ul li a:hover {
filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#26FFFFFF', endColorstr='#26FFFFFF'); background: rgba(255,255,255,0.15); opacity: 1; }
.admincp-header-r { float: right; }
.admincp-header-r .manager,
.admincp-header-r .operate { vertical-align: top; display: inline-block; *display: inline;
*zoom: 1;
}
.admincp-header-r .manager { height: 30px; padding: 6px 0; position: relative; z-index: 2; margin-top: 4px; }
.admincp-header-r .manager dl {text-align: right; vertical-align: top; display: inline-block; *display: inline;
margin-right: 10px; *zoom: 1;
}
.admincp-header-r .manager dt { line-height: 16px;font-size: 14px; }
.admincp-header-r .manager dd { line-height: 16px;font-size: 12px; color: #999; }
.admincp-header-r .manager .avatar { background-color: transparent; vertical-align: top; display: inline-block; *display: inline;
width: 32px; height: 34px;  *zoom: 1;
position: relative; z-index: 1; overflow: hidden; }
.admincp-header-r .manager .avatar .admin-avatar-file { width: 32px; height: 32px; opacity: 0; filter: alpha(opacity=0); position: absolute; z-index: 2; top: 1px; left: 1px; cursor: pointer; }
.admincp-header-r .manager .avatar img { height: 32px; width:32px;  position: absolute; z-index: 1; top: 2px; left: 0;border-radius: 18px; }
.admincp-header-r .manager .arrow { font-size: 0px; line-height: 0; vertical-align: top; display: inline-block; width: 0px; height: 0px; margin-left: 10px; margin-top: 15px; margin-right: 10px; border-width: 4px; border-color: #333 transparent transparent transparent; border-style: solid dashed dashed dashed; cursor: pointer; }
.admincp-header-r .manager .arrow-close { font-size: 0px; line-height: 0; vertical-align: top; display: inline-block; width: 0px; height: 0px; margin-left: 10px; margin-top: 10px; margin-right: 10px; border-width: 4px; border-color: transparent transparent #333 transparent; border-style: dashed dashed solid dashed; cursor: pointer; }
.admincp-header-r .manager-menu { background-color: #FFF; display: none; min-width: 220px; padding: 0 8px 8px 8px; border: solid 1px #D7D7D7; position: absolute; z-index: 2; right: 2px; top: 44px; box-shadow: 0 0 4px rgba(0,0,0, 0.25); }
.admincp-header-r .manager-menu .title { line-height: 22px; height: 22px; margin-top: 8px; margin-bottom: 2px; border-bottom: dotted 1px #DDD; overflow: hidden; }
.admincp-header-r .manager-menu .title h4 { font-weight: 600; font-size: 12px; color: #333; display: inline-block; }
.admincp-header-r .manager-menu .title a { font-size: 0; background: transparent url(../images/combine_img.png) no-repeat; width: 60px; height: 16px; float: right; margin-top: 4px; border-radius: 2px; }
.admincp-header-r .manager-menu .title a:hover { background-color: #999; }
.admincp-header-r .manager-menu .title a.edit-password { background-position: 0px -260px; }
.admincp-header-r .manager-menu .title a.add-menu { background-position: -60px -260px; }
.admincp-header-r .manager-menu .title a:hover.edit-password { background-position: -0px -240px; }
.admincp-header-r .manager-menu .title a:hover.add-menu { background-position: -60px -240px; }
.admincp-header-r .manager-menu .login-date { font-size: 11px; font-family: Tahoma; color: #555; padding: 0 8px; }
.admincp-header-r .manager-menu .login-date span { color: #999; display: block; }
.admincp-header-r .manager-menu li { width: 50%; }
.admincp-header-r .manager-menu li a { line-height: 24px; color: #777; padding-left: 8px; }
.admincp-header-r .operate { height: 48px; border-left: solid 1px rgba(255,255,255,0.25); box-shadow: -1px 0 0 rgba(0,0,0,0.15); }
.admincp-header-r .operate li { width: 48px; position: relative; z-index: 1; }
.admincp-header-r .operate li:hover {
filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#26FFFFFF', endColorstr='#26FFFFFF'); background: rgba(255,255,255,0.15); }
.admincp-header-r .operate li a { font-size: 0; background: url(../images/combine_img.png) no-repeat; display: block; width: 48px; height: 48px; position: relative; z-index: 1; }
.admincp-header-r .operate li a.sitemap { background-position: 0 -180px; }
.admincp-header-r .operate li a.style-color { background-position: -50px -180px; }
.admincp-header-r .operate li a.homepage { background-position: -100px -180px; }
.admincp-header-r .operate li a.login-out { background-position: -150px -180px; }
.admincp-header-r .operate li a.toast { background-position: -192px -180px; }
.admincp-header-r .operate li a em,.update em { font-size: 10px; font-weight: 600; line-height: 14px; color: #FFF; background-color: #F30; text-align: center; display: block; min-width: 10px; height: 14px; padding: 0 2px; border: solid 2px #FFF; border-radius: 9px; position: absolute; z-index: 1; top: 2px; right: 1px; -webkit-animation: twinkling 1s infinite ease-in-out; }
.update em{ float:right;margin: 16px 20px 0 0;}
 @-webkit-keyframes 'twinkling' { /*透明度由0到1*/
0% {
opacity:0;  /*透明度为0*/
}
100% {
opacity:1;  /*透明度为1*/
}
}
.admincp-header-r .operate li .sub-menu { position: absolute; z-index: 1; }
/* 头部样式 */
div.bgSelector { width: 100%; text-align: left; display: none; }
div.bgSelector div#bgColorSelector { width: 100%; overflow: hidden; }
div.bgSelector div#bgColorSelector #bscParent { position: relative; width: 100%; height: 100%; overflow: hidden; z-index: 9998; }
div.bgSelector div#bgColorSelector #bscParent #bscDrag img { position: relative; z-index: -1; width: 47px; height: 35px; cursor: ew-resize }
div.bgSelector div#bgColorSelector #bscParent #bscDrag { position: absolute; left: 0; float: left; z-index: 9999; }
div.bgSelector div#bgColorSelector #bscParent #bscDrop { width: 100%; margin: 0; padding: 0; }
div.bgSelector div#bgColorSelector #bscParent #bscDrop td { margin: 0; padding: 0; }
div.bgSelector div#bgColorSelector #bscParent #bscDrop td span { display: block; width: 100%; }
.alert_img { position: absolute; top: 85px; z-index: 9; left: 736px; }
div.nav2Panel { width: 890px; height: 38px; padding: 0 30px; z-index: 999; margin: -5px auto 0; background-color: #fcfcfc; border: 1px #dee0df solid; border-top: 0; }
#foldSidebar { width: 16px; height: 16px; position: absolute; z-index: 99;bottom: 20px;left: 18px; }
#foldSidebar i { font-size: 24px;color:#3398cc; }
/* 容器
******************************/
.admincp-container { background-color: transparent; height: 100%; position: relative; z-index: 4; }

.admincp-container-right { background-color: #FFF; position: absolute; z-index: 1; top: 0px; right: 0; bottom: 0; left: 166px; }
.admincp-container-right .top-border {}
.fold .admincp-container-right { background-color: #FFF; position: absolute; z-index: 1; top: 0px; right: 0; bottom: 0; left: 0px; }
.flexigrid { position: relative; z-index: 1; overflow: hidden; }
/*f表格标题块*/
.flexigrid .mDiv { background-color: #FFF; color: #333; white-space: nowrap; display: block;  position: relative; z-index: 4; padding: 16px 0px;min-height: 30px; }
/*标题*/
.flexigrid .mDiv .ftitle { font-size: 0; *word-spacing:-1px/*IE6、7*/;
height: 24px; float: left; }
.flexigrid .mDiv .ftitle h3,
.flexigrid .mDiv .ftitle h5 { font-size: 12px; font-weight: normal; line-height: 24px; color: #777; vertical-align: bottom; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
padding: 0; *zoom:1/*IE7*/; 
}
.flexigrid .mDiv .ftitle h3 { font-size: 14px; color: #333; margin-right: 6px; display: inline-block;
    text-indent: 8px;}
.flexigrid .ftitle{margin-bottom: 10px;}
.flexigrid .mDiv .ftitle_nav { font-size: 0; *word-spacing:-1px/*IE6、7*/;
height: 28px; float: left; }
.flexigrid .mDiv .ftitle_nav h3,
.flexigrid .mDiv .ftitle_nav h5 { font-size: 12px; font-weight: normal; line-height: 24px; color: #777; vertical-align: bottom; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
padding: 0; *zoom:1/*IE7*/; 
}
.flexigrid .mDiv .ftitle_nav h3 { font-size: 14px; color: #333; margin-right: 6px; display: inline-block;
    text-indent: 8px;}
.flexigrid .ftitle_nav{margin-bottom: 10px;}
.anchor-icon{
    display:inline-block;
    width: 22px;
    height: 20px;
    background-color: red;
    margin-right: 6px;
    vertical-align:middle;
}
.anchor-icon-biao{
    background: url(../images/icon-shang.png) no-repeat;
} 

/*数据统计数*/
.flexigrid .pReload { color: #999; background-color: #FFF; width: 24px; height: 24px; float: left; text-align: center; line-height: 24px; margin: 0 0 0 10px; position: relative; z-index: 1; }
.flexigrid .pReload:hover { color: #333; background-color: #E6E6E6; border-radius: 5px; cursor: pointer; }
.flexigrid .pReload i { font-size: 14px; margin: 0; }
.flexigrid .pReload.loading i { color: #090; display: inline-block; -webkit-animation: fa-spin 0.2s infinite linear; animation: fa-spin 0.2s infinite linear; }
@-webkit-keyframes fa-spin {
0% {
-webkit-transform: rotate(0deg);
transform: rotate(0deg);
}
100% {
-webkit-transform: rotate(359deg);
transform: rotate(359deg);
}
}
@keyframes fa-spin {
0% {
-webkit-transform: rotate(0deg);
transform: rotate(0deg);
}
100% {
-webkit-transform: rotate(359deg);
transform: rotate(359deg);
}
}
/*操作间隔*/
.flexigrid .mDiv .sline { width: 0; height: 16px; float: left; margin: 4px; border-left: dotted 1px #D7D7D7; }
/*操作按钮*/
.flexigrid .nBtn { color: #777; background-color: #FFF; width: 100px; height: 24px; float: left; position: relative; z-index: 1; cursor: pointer; }
.flexigrid .nBtn .hBtn { text-align: center; line-height: 24px; width: 24px; height: 24px; border: solid 1px transparent; position: absolute; z-index: 2; top: 0; left: -1px; }
.flexigrid .nBtn:hover .hBtn { color: #333; background-color: #FFF; border-radius: 4px 4px 0 0; border-color: #D7D7D7; border-bottom: 0; }
.flexigrid .nBtn i { font-size: 14px; margin: 0; }
/*菜单配置下拉框*/
.flexigrid .nDiv { background-color: #FFF; padding: 5px 0 5px 5px; border: 1px solid #D7D7D7; border-radius: 0 0 4px 4px; overflow: auto; position: absolute; z-index: 1; top: 23px; left: -1px; box-shadow: 3px 3px 0 rgba(0,0,0,0.05); }
.flexigrid .nDiv h5 { font-size: 12px; font-weight: normal; line-height: 24px; color: #F60; }
.flexigrid .nDiv .ps-container { display: block; max-height: 150px; padding: 0 10px 10px 0; overflow: hidden; position: relative; z-index: auto; }
.flexigrid .nDiv ul { width: 100%; }
.flexigrid .nDiv li { font-size: 0; *word-spacing:-1px/*IE6、7*/;
line-height: 20px; text-align: left; padding: 2px 10px 2px 0; border-bottom: dotted 1px #E6E6E6; }
.flexigrid .nDiv li:hover { color: #666; background: #F5F5F5; }
.flexigrid .nDiv li span { font-size: 12px; vertical-align: middle; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/;
}
.flexigrid .nDiv li span.ndcol1 { margin-right: 5px; }
.flexigrid .nDiv .ps-scrollbar-x-rail { display: none !important; }
/*表格搜索*/
.flexigrid .sDiv { position: relative;float: right; overflow: hidden; }
.member_lt .sDiv .p_btn{padding: 6px 0px 6px 6px;}
.flexigrid .sDiv .btn{ color: #555;font-size: 14px; }
.flexigrid .sDiv .cur{ color: #3478bc;}
.flexigrid .sDiv i.bar {
    vertical-align: middle;
    display: inline-block;
    height: 10px;
    width: 1px;
    background: #ccc;
}
.flexigrid .sDiv2 { font-size: 0; *word-spacing:-1px/*IE6、7*/;
display: inline-block; vertical-align: middle; *display: inline/*IE7*/;*zoom:1/*IE7*/;position: relative;border: solid 1px #eee; }
.flexigrid .sDiv2:hover { border-color: #e7e7e7; }
.flexigrid .sDiv2 .qsbox,
.flexigrid .sDiv2 .select,
.flexigrid .sDiv2 .btn { border: none; border-radius: 0; font-size: 12px; vertical-align: top; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/;
border-radius: 0; }
.flexigrid .sDiv2 .select { background-color: #FFF; width: auto; height: 26px; margin: 0px !important; }
.flexigrid .sDiv2 .qsbox { background-color: #FFF; font-size: 12px; line-height: 24px; width: 120px; height: 24px; padding: 1px 30px 1px 5px; }
.flexigrid .sDiv2 .qsbox::-webkit-input-placeholder { /* WebKit, Blink, Edge */    color: #9e9e9e; }
.flexigrid .sDiv2 .qsbox:-moz-placeholder { /* Mozilla Firefox 4 to 18 */   color: #9e9e9e; }
.flexigrid .sDiv2 .qsbox::-moz-placeholder { /* Mozilla Firefox 19+ */   color: #9e9e9e; }
.flexigrid .sDiv2 .qsbox:-ms-input-placeholder { /* Internet Explorer 10-11 */   color: #9e9e9e; }
.flexigrid .sDiv2  .sDiv3{width: 100px;}
.flexigrid .sDiv2 .qsbox:hover, .flexigrid .sDiv2 .qsbox:fouce {box-shadow: none;}

.flexigrid .sDiv2 .btn {position: absolute;width: 30px; height: 26px; cursor: pointer; background-color: #fff;  color: #666;top:0;right: 0;opacity: 0;  z-index: 2;}
.flexigrid .sDiv2 .fa-search{position: absolute;top:6px;right: 4px;color:rgb(136, 136, 136);}
.flexigrid .sDiv2 .e-sousuo{position: absolute;top:6px;right: 4px;color:#444;}

.flexigrid .sDiv2 .btn2 { height: 26px; cursor: pointer;padding: 2px 12px;color: #fff;
/*    background-color: #FF6666;
    border-color: #FF6666;*/
    border-radius: 20px;border: none;}
.flexigrid .sDiv2 .btn2:hover { color: #FFF; background-color: #FF6666; }
.flexigrid .sDiv .a_search { color: #FF6666; display: inline-block; vertical-align: middle; *display: inline/*IE7*/;
margin-left: 8px; *zoom:1/*IE7*/;
}
.flexigrid .sDiv .a_search i { font-size: 14px; margin-right: 4px; }
.flexigrid .sDiv .a_search:hover { color: #CD0200; }

@media screen and (max-width: 1280px){
    .flexigrid .sDiv2 .qsbox { width: 100px;}
    .flexigrid .sDiv2  .sDiv3{width: 90px;}
}
@media screen and (max-width: 1180px){
 .flexigrid .sDiv2 .qsbox { width: 90px;}
}
@media screen and (max-width: 1080px){
 .flexigrid .sDiv2 .qsbox { width: 72px;}
 .flexigrid .sDiv2  .sDiv3{width: 80px;}
}

/*数据表格头*/
.flexigrid .hDiv { background-color: #f7f7f7; clear: both; position: relative; z-index: 1; overflow: hidden; }
.flexigrid .hDiv table { }
.flexigrid .hDiv th div { }
.flexigrid .hDivBox { float: none; }
.flexigrid .hDiv i.ico-check { background: url(../images/flexigrid_pic.png) no-repeat 0 0; display: inline-block; width: 20px; height: 20px; cursor: pointer; }
.flexigrid .hDiv .trSelected i.ico-check { background-position: -20px 0; }
.flexigrid .hDiv .sorted { background-color: #E6E6E6; }
.flexigrid .hDiv .thOver { }
.flexigrid .hDiv .thOver div,
.flexigrid .hDiv .sorted.thOver div { padding-bottom: 6px; border-bottom: 2px solid #3b639f; cursor: pointer; }
.flexigrid .hDiv .sorted div { padding-bottom: 5px; }
.flexigrid .hDiv .thMove { background: #fff; color: #fff; }
.flexigrid .hDiv .sorted.thMove div { border-bottom: 1px solid #fff; padding-bottom: 4px }
.flexigrid .hDiv .thMove div { background: #fff !important; }
.flexigrid .hDiv th .sdesc { background: url(../images/dn.png) no-repeat center top; }
.flexigrid .hDiv th .sasc { background: url(../images/up.png) no-repeat center top; }
/*调节表格列宽*/
.flexigrid .cDrag { float: left; position: absolute; z-index: 2; overflow: visible; }
.flexigrid .cDrag div { background: none; display: block; width: 3px; height: 24px; float: left; position: absolute; cursor: col-resize; }
.flexigrid .cDrag div:hover,
.flexigrid .cDrag div.dragging {
filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#19000000', endColorstr='#19000000'); background: rgba(0,0,0,0.1); border: solid #FFF; border-width: 0 1px; }
/*批量操作条*/
.flexigrid .tDiv { position: relative; z-index: 3; overflow: hidden; border-bottom: 1px solid #f7f7f7; border-top: none;}
.flexigrid .tDiv2 { font-size: 0; *word-spacing:-1px/*IE6、7*/;
padding: 6px 0 6px 6px; overflow: hidden; }
.flexigrid .btnseparator { float: left; height: 22px; border-left: 1px solid #ccc; border-right: 1px solid #fff; margin: 1px; }
.flexigrid .fbutton { vertical-align: top; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/; margin-left: 8px;  *zoom:1/*IE7*/;
cursor: pointer; }
.flexigrid .fbutton div {font-size: 13px;line-height: 20px;color: #555;border: 1px solid #ddd;background-color: #F7F7F7;padding: 3px 9px;}
.flexigrid .fbutton div.hover:hover{background-color: #fff;}
.flexigrid .tDiv .fbutton .layui-btn {padding: 0 10px;font-size: 12px;}
.flexigrid .tDiv .fbutton .layui-btn-primary {border: 1px solid #ddd;background-color: #F7F7F7;line-height: 28px;height: 30px;border-radius: 0;}
.flexigrid .tDiv .fbutton .layui-btn-primary:hover {background-color: #fff;}
.flexigrid .fbuttonr {float: right;display: inline;padding-right: 8px;margin-left: 8px;zoom: 1;}
.flexigrid .fbuttonr .total {line-height: 26px;}
.flexigrid .fbuttonr .total select{margin: 0px 5px;width: 50px;height: 30px;line-height: 30px;}

.flexigrid .fbutton div.dx{ color: #ff0000; }
.flexigrid .fbutton .add:hover,
.flexigrid .fbutton.fbOver .add:hover {background: #fff;}
.flexigrid .fbutton .del:hover,
.flexigrid .fbutton.fbOver .del:hover { color: #E84C3D; border-color: #E84C3D; }
.flexigrid .fbutton .csv:hover,
.flexigrid .fbutton.fbOver .csv:hover { color: #9C59B8; border-color: #9C59B8; }
/*数据表格内容部分*/
.flexigrid .bDiv { background: #fff; position: relative; z-index: 2; overflow: auto;}
.flexigrid .bDiv table { width: 100%; }
.flexigrid .bDiv td { color: #555; vertical-align: top; white-space: nowrap; padding: 0; margin: 0; border-bottom: 1px solid E6E6E6; }
.flexigrid .bDiv td.name div{ white-space: nowrap;text-overflow:ellipsis; }
.flexigrid .bDiv td.sign input,.flexigrid .hDiv th.sign input{ margin: 5px 8px; }
.flexigrid .bDiv td div {}
.flexigrid .bDiv a { color: #333; }
.flexigrid .bDiv a u{ text-decoration:none; }
.flexigrid .bDiv a:hover { text-decoration: underline;}
.flexigrid .bDiv td i.ico-check { background: url(../images/flexigrid_pic.png) no-repeat 0 0; display: inline-block; width: 20px; height: 20px; }
.flexigrid .bDiv .trSelected td i.ico-check { background-position: -20px 0; }
.flexigrid .bDiv td .on,
.flexigrid .bDiv td .yes { color: #41BEDD !important; cursor: pointer; }
.flexigrid-xin .tableDiv td .on,
.flexigrid-xin .tableDiv td .yes { color: #41BEDD !important; cursor: pointer; }
.flexigrid .bDiv td .off,
.flexigrid .bDiv td .no { color: #9ea3a7 !important; cursor: pointer; }
.flexigrid-xin .tableDiv td .off,
.flexigrid-xin .tableDiv td .no { color: #9ea3a7 !important; cursor: pointer; }
.flexigrid .bDiv td.normal { color: #C30; }
.flexigrid .bDiv td.abnormal { color: #090; }
.flexigrid .bDiv td.column-a { background-color: #F6EEFB; }
.flexigrid .bDiv td.column-b { background-color: #F9F3D7; }
.flexigrid .bDiv td.column-c { background-color: #FBEEE0; }

.flexigrid .bDiv .typename,.flexigrid .hDiv th div.sunone{ padding-left: 15px; }

/*数据表格查看操作项*/
.flexigrid .bDiv .handle div { font-size: 0; *word-spacing:-1px/*IE6、7*/;
}
.flexigrid .bDiv td.operation div i{
    vertical-align: middle;
    display: inline-block;
    height: 10px;
    width: 1px;
    background: #ccc;
}
.flexigrid-xin .tableDiv td.operation div i{
    vertical-align: middle;
    display: inline-block;
    height: 10px;
    width: 1px;
    background: #ccc;
}
.flexigrid .bDiv a.btn , a.ui-btn{  font-weight: normal; line-height: 20px; color: #3478bc; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;*zoom:1/*IE7*/;  font-size:13px;height: 20px; padding: 1px 5px;  cursor: pointer;position: relative; }

.flexigrid .bDiv a.btn .num{width: 14px; height: 14px; border-radius: 50px;background-color: red;color: #fff;font-size: 12px;line-height: 14px;position: absolute;right:-7px;top: -7px;}
a.ui-btn3{  font-weight: normal; line-height: 20px; color: #666; background: #eee none; vertical-align: top; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;*zoom:1/*IE7*/;height: 20px; padding: 0px 8px;  cursor: pointer !important;height: 26px; line-height: 26px; font-size: 12px;}
a.ui-btn{ height: 26px; line-height: 26px; font-size: 12px;}
.flexigrid .bDiv a.btn i{ display: none; }
.flexigrid .bDiv a:hover{color:#039;}
.flexigrid .bDiv .handle span { font-size: 12px; }
.flexigrid .bDiv span.btn { font-size: 12px; vertical-align: top; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/;
position: relative; z-index: 1; cursor: default; }
.flexigrid .bDiv span.btn:hover { z-index: 3; box-shadow: 2px 2px 0 rgba(0,0,0,0.1); }
.flexigrid .bDiv span.btn em { color: #777; line-height: 20px; background-color: #FFF; display: block; height: 20px; padding: 1px 6px; border: solid 1px #F5F5F5; border-radius: 4px 0 0 4px; position: relative; z-index: 2; }
.flexigrid .bDiv span.btn:hover em { color: #FFF; background-color: #4fc0e8; border-color: #3aa8cf; }
.flexigrid .bDiv span.btn em i { font-size: 14px; vertical-align: middle; margin-right: 4px; }
.flexigrid .bDiv span.btn em .arrow { font-size: 0px; line-height: 0; vertical-align: middle; display: inline-block; width: 0px; height: 0px; float: none; margin: 0 0 0 4px; border-width: 4px; border-color: #999 transparent transparent transparent; border-style: solid dashed dashed dashed; -webkit-transition: .2s ease-in; -moz-transition: -webkit-transform .2s ease-in; -o-transition: -webkit-transform .2s ease-in; transition: .2s ease-in; }
.flexigrid .bDiv span.btn:hover .arrow { border-color: #FFF transparent transparent transparent; FILTER: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
-moz-transform: rotate(270deg); -moz-transform-origin: 50% 30%; -webkit-transform: rotate(270deg); -webkit-transform-origin: 50% 30%; -o-transform: rotate(270deg); -o-transform-origin: 50% 30%; transform: rotate(270deg); transform-origin: 50% 30%; }
.flexigrid .bDiv span.btn ul { background-color: #4fc0e8; display: none; border: solid 1px #3aa8cf; border-radius: 0 4px 4px 0; margin-left: -2px; position: absolute; z-index: 1; top: 0; left: 100%; overflow: hidden; box-shadow: 3px 3px 0 rgba(0,0,0,0.1); }
.flexigrid .bDiv tr:last-child span.btn ul { bottom: 0; top: auto; }
.flexigrid .bDiv tr:first-child span.btn ul { bottom: auto; top: 0; }
.flexigrid .bDiv tr:nth-last-child(2) span.btn ul { bottom: auto; top: 0; }
.flexigrid .bDiv span.btn:hover ul { display: block; padding: 0 5px; }
.flexigrid .bDiv span.btn ul li { border-bottom: dotted 1px #3aa8cf; clear: both; margin-bottom: -1px; }
.flexigrid .bDiv span.btn ul li a { color: #FFF; display: block; line-height: 24px; text-align: right; height: 24px; padding: 0 5px; }
.flexigrid .bDiv span.btn ul li a:hover { text-decoration: underline; }
.flexigrid .bDiv span.btn ul li a.expire { color: #FF0 !important; }
.flexigrid .bDiv a.expired { color: #F30; }
.flexigrid .bDiv a.expire { color: #F93; }
i.fa-external-link { color: #999 !important; font-size: 9px !important; margin-left: 4px; margin-right: 0; }
.flexigrid .bDiv .user-avatar { background-color: #FFF; vertical-align: middle; display: inline-block; width: 22px; height: 22px; margin-right: 6px; border: solid 1px #D7D7D7; border-radius: 50%; }
.flexigrid .bDiv .pic-thumb-tip { color: #777; }
.flexigrid .bDiv .pic-thumb-tip i { font-size: 14px; }
.flexigrid .bDiv .pic-thumb-tip:hover { color: #333; }

.flexigrid .bDiv .tagBox{width: 98%; margin: 0 auto;background-color: #fff;padding:10px;}
.flexigrid .bDiv .tagBox a{margin: 4px 0;}
/*数据表格查询为空*/
.no-data,
.flexigrid .bDiv .no-data { font-size: 14px; color: #999; width: 100% !important; height: 24px; border: 0 none !important; text-align: center !important; padding: 100px 0 !important; }
.flexigrid .bDiv .trSelected .no-data,
.flexigrid .bDiv .no-data:hover { color: #999 !important; background-color: transparent !important; }
.no-data i,
.flexigrid .bDiv .no-data i { font-size: 18px; margin-right: 6px; color: #FC0; }
.flexigrid .bDiv .handle-btn { }
/*hDiv\bDiv\colCopy同步项*/
.flexigrid .hDiv th,
.flexigrid .bDiv td { vertical-align: middle; text-align:left; font-size: 13px !important; overflow: visible;  border-bottom: 1px solid #f7f7f7;font-family: "Segoe UI", "Lucida Grande", Helvetica, Arial, "Microsoft YaHei", FreeSans, Arimo, "Droid Sans","wenquanyi micro hei","Hiragino Sans GB", "Hiragino Sans GB W3", Arial, sans-serif;}
.flexigrid .bDiv td div img{ float: left; }
.flexigrid .hDiv th div,
.flexigrid .bDiv td div,.colCopy div { display: block; line-height: 30px; text-overflow: ellipsis; white-space: nowrap;  padding: 3px 0px; overflow: hidden;}
.sort_style {
    cursor: pointer;
}
.sort_style a{ color:#555}
.sort_style i {
    display: inline-block;
    width: 7px;
    height: 10px;
    background: url(../images/sort.png);
    margin-left: 5px;
    background-position: 0 -20px;
}
.sort_style i.desc {
    background-position: 0 0;
}
.sort_style i.asc {
    background-position: 0 -10px;
}

.flexigrid .bDiv td div { padding: 14px 0px; white-space: normal; }
.flexigrid .bDiv td div .binding{ padding-left: 10px; }
.flexigrid .bDiv td div .binding img{ margin-right: 5px; }
.flexigrid .hDiv .handle div,
.flexigrid .bDiv .handle div { overflow: visible; min-width: 150px !important; max-width: 174px !important; }
.flexigrid .hDiv .handle-s div,
.flexigrid .bDiv .handle-s div { overflow: visible; min-width: 60px !important; max-width: 60px !important; }
.flexigrid.hideBody { height: 26px !important; border-bottom: 1px solid #ccc; }
.ie6fullwidthbug { border-right: 0px solid #ccc; padding-right: 2px; }
.flexigrid .bDiv table.autoht { width: 100%; margin-bottom: 0px; border-bottom: 0px; }
.flexigrid .nBtn.srtd { background: url(../images/wbg.gif) repeat-x 0px -1px; }
.flexigrid .hDiv th,.colCopy {  white-space: nowrap; cursor: default; overflow: hidden; border-right: 2px solid #fff;box-sizing: border-box;}
.htitx th{ border-right: none !important;}
.htitx .plug-list{ padding: 20px 10px 0;}
.htitx .plug-item-content{ width:28%;}
.colCopy { color: #333; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#E5FFFFFF', endColorstr='#E5FFFFFF');
background: rgba(255,255,255,0.9); border: dotted 1px #D7D7D7; overflow: hidden; box-shadow: 4px 4px 0 rgba(0,0,0,0.05); z-index: 9; }
.flexigrid tr td.sorted { background-color: #F9F9F9; border-bottom: 1px solid #E6E6E6 }
.flexigrid tr td.sorted div { bckground: #FFF7CF }
.flexigrid tr.erow td { background: #FDFDFD; border-bottom: 1px solid #F5F5F5; }
.flexigrid tr.erow td.sorted { background: #F0F0F0; border-bottom: 1px solid #E6E6E6; }
.flexigrid tr.erow td.sorted div { }
.flexigrid div.bDiv tr:hover td,
.flexigrid div.bDiv tr:hover td.sorted,
.flexigrid div.bDiv tr.trOver td.sorted,
.flexigrid div.bDiv tr.trOver td { background: #F4FCFA; }

.flexigrid .pDiv { background-color: #F5F5F5; border-style: solid; border-color: #D7D7D7 transparent #C8C8C8 transparent; border-width: 1px 0; position: relative; z-index: 3; }
.flexigrid .pDiv .pDiv2 { padding: 6px 0; margin: 0; border-color: #FFF; border-style: solid; border-width: 1px 0; position: relative; z-index: 1; }
.flexigrid .pGroup-left { color: #777; line-height: 24px; height: 24px; position: absolute; z-index: 1; top: 6px; left: 12px; }
.flexigrid .pGroup-left .select { font-family: Arial, Helvetica, sans-serif; vertical-align: middle; display: inline-block; *display: inline;
width: 40px; height: 24px; background-color: transparent; padding: 0; margin: 0 6px 0 0; border-radius: 0; border: none; *zoom: 1;
}
.flexigrid .pGroup-right { color: #777; line-height: 24px; height: 24px; position: absolute; z-index: 1; top: 6px; right: 12px; }
/*f表格翻页控制*/
.flexigrid .pGroup-middle { font-size: 0; *word-spacing:-1px/*IE6、7*/;
text-align: center; margin: 0 auto; width: 250px; height: 24px; float: none; z-index: auto; }
.flexigrid .pGroup-middle .pButton,
.flexigrid .pGroup-middle .pcontrol,
.flexigrid .pGroup-middle .pcontrol input,
.flexigrid .pGroup-middle .pcontrol span { vertical-align: middle; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/;
}
.flexigrid .pGroup-middle .pButton { color: #999; background-color: transparent; text-align: center; width: 24px; height: 24px; padding: 0; margin-right: 8px; border-radius: 4px; cursor: pointer; overflow: hidden; }
.flexigrid .pGroup-middle .pButton:hover { color: #333; background-color: #CCC; padding: 0; border: 0; }
.flexigrid .pGroup-middle .pButton i { line-height: 24px; font-size: 14px; }
.flexigrid .pGroup-middle .pcontrol { font-size: 12px; line-height: 20px; color: #999; *word-spacing:-1px/*IE6、7*/;
margin-right: 10px; }
.flexigrid .pGroup-middle .pcontrol input { color: #333; background-color: transparent; text-decoration: underline; text-align: center; width: 20px; padding: 0; border: none; border-radius: 0; }
.flexigrid .pGroup-middle .pcontrol input:hover,
.flexigrid .pGroup-middle .pcontrol input:focus { background-color: #FFF; box-shadow: none; }
.pPageStat { display: block; line-height: 40px; text-align: center; color: #FFF; position: absolute; top: -40px; left: 0px; right: 0px; filter:progid:DXImageTransform.Microsoft.gradient(enabled='true', startColorstr='#7F000000', endColorstr='#7F000000');
background: rgba(0,0,0,0.5); width: 100%; }
.flexigrid .pGroup-middle .pcontrol span { margin-right: 2px; }
.flexigrid .pDiv .pButton.pBtnOver { width: 20px; height: 20px; border: 1px solid #ccc; cursor: pointer; }
.flexigrid .pDiv .pButton span { display: block; width: 20px; height: 20px; float: left; }
.flexigrid .pDiv .pButton:hover span,
.flexigrid .pDiv .pButton.pBtnOver span { width: 19px; height: 19px; border-top: 1px solid #fff; border-left: 1px solid #fff; }
.flexigrid div.iDiv { border: 1px solid #316ac5; position: absolute; overflow: visible; background: none; }
.flexigrid div.iDiv input,
.flexigrid div.iDiv select,
.flexigrid div.iDiv textarea { font-family: Arial, Helvetica, sans-serif; font-size: 11px; }
.flexigrid div.iDiv input.tb { border: 0px; padding: 0px; width: 100%; height: 100%; padding: 0px; background: none; }
.flexigrid div.hGrip { position: absolute; top: 0px; right: 0px; height: 5px; width: 5px; background: url(../images/line.gif) repeat-x center; margin-right: 1px; cursor: col-resize; }
.flexigrid div.hGrip:hover,
.flexigrid div.hGrip.hgOver { border-right: 1px solid #999; margin-right: 0px; }
.flexigrid div.vGrip { height: 5px; overflow: hidden; position: relative; background: #fafafa url(../images/wbg.gif) repeat-x 0px -1px; border: 1px solid #ccc; border-top: 0px; text-align: center; cursor: row-resize; display: none; }
.flexigrid div.vGrip span { display: block; margin: 1px auto; width: 20px; height: 1px; overflow: hidden; border-top: 1px solid #aaa; border-bottom: 1px solid #aaa; background: none; }
.flexigrid span.cdropleft { display: block; background: url(../images/prev.gif) no-repeat -4px center; width: 24px; height: 24px; position: relative; top: -24px; margin-bottom: -24px; z-index: 3; }
.flexigrid div.hDiv span.cdropright { display: block; background: url(../images/next.gif) no-repeat 12px center; width: 24px; height: 24px; float: right; position: relative; top: -24px; margin-bottom: -24px; }
/* ie adjustments */
.flexigrid.ie .hDiv th div,
.colCopy.ie div/* common inner cell properties*/ { overflow: hidden; }
/* 同步调用表格时搜索栏 */
.ncap-search-ban-s { color: #FFF; background-color: #16a086; width: 16px; padding: 8px 3px 8px 5px; margin-top: -80px; border: solid 1px #16a086; border-right: 0 none; position: fixed; display: block; z-index: 99; top: 50%; right: 0; cursor: pointer; box-shadow: 0 0 5px 0 rgba(204,204,204,0.5); }
.ncap-search-bar-s i { margin: 0 0 5px 0; }
.ncap-search-bar { background-color: #F5F5F5; border-left: solid 1px #D7D7D7; height: 100%x; padding: 10px 0 10px 10px; position: fixed; z-index: 99; top: 0; bottom: 0; right: -230px; }
.ncap-search-bar .title { display: block; }
.ncap-search-bar .title h3 { color: #333; font-size: 16px; font-weight: normal; line-height: 20px; }
.ncap-search-bar .handle-btn { color: #999; background-color: #F5F5F5; width: 16px; padding: 8px 3px 8px 5px; margin-top: -80px; border: solid 1px #E7E7E7; border-right: 0 none; position: absolute; z-index: 1; left: -25px; top: 50%; cursor: pointer; }
.ncap-search-bar .handle-btn i { margin-bottom: 5px; }
.ncap-search-bar .content { width: 156px; display: block; padding-right: 15px; margin-bottom: 50px; position: relative; z-index: 1; overflow: hidden; }
.ncap-search-bar .content .layout-box { display: block; }
.ncap-search-bar dl { padding: 5px 0; border-bottom: solid 1px #E6E6E6; box-shadow: 0 1px 0 rgba(255,255,255, 0.75); }
.ncap-search-bar dt { line-height: 20px; color: #808B8D; padding: 0 0 2px 4px; }
.ncap-search-bar dd { }
.ncap-search-bar dd label { display: block; margin-bottom: 5px; }
.ncap-search-bar dd .s-input-txt { background-color: #FFF; width: 140px; }
.ncap-search-bar dd .s-select { width: 150px; }
.ncap-search-bar dd .querySelect,
.ncap-search-bar dd .class-select { width: 150px; margin-bottom: 5px; }
.ncap-search-bar .bottom { background-color: #F5F5F5; padding: 10px 0 15px 0; border-top: solid 1px #E7E7E7; text-align: center; position: absolute; z-index: 2; left: 0; right: 0; bottom: 0; }
.ncap-form-default { padding: 10px 0 30px 0; overflow: hidden; }
.ncap-form-default .title { padding: 10px 0; border-bottom: solid 1px #C8C8C8; }
.ncap-form-default .title h3 { font-size: 16px; line-height: 20px; color: #333; font-weight: normal; }
.ncap-form-default dl.row { font-size: 0; color: #777; background-color: #FFF; *word-spacing:-1px/*IE6、7*/;
padding: 12px 0; margin-top: -1px; border-style: solid; border-width: 1px 0; border-color: #f7f7f7; position: relative; z-index: 1; }
.ncap-form-default dl.row:first-child { border-top-color: #FFF; }
.ncap-form-default dl.row:nth-child(even) { background-color: #FDFDFD; }
.ncap-form-default dt.sort-e,
.ncap-form-default dt.tit,
.ncap-form-default dd.opt { font-size: 12px; line-height: 24px; vertical-align: top; letter-spacing: normal; display: inline-block; *display: inline/*IE7*/;
*zoom:1/*IE7*/; font-size:14px;}

.ncap-form-default dd.opt input[type=checkbox],.ncap-form-default dd.opt input[type=radio]{ margin-right: 3px;}
.ncap-form-default dt.sort-e { text-align: left; width: 58px; padding-left: 34px; }
.ncap-form-default dt.tit { text-align: left; width: 100px; padding-left: 34px; }
.ncap-form-default.help dt.tit { text-align: left; width: 100px; padding-left: 0; }
.ncap-form-default dd.opt { text-align: left; width: 83%;}
.ncap-form-default.help dd.opt { text-align: left; width: 75%;}
.ncap-form-default dd.opt .opt-moreOper{position: relative;overflow: hidden;margin-top: 10px; display: block;height: 28px;}
.ncap-form-default dd.opt .opt-moreOper p{position: absolute;z-index: 999;}
.ncap-form-default dd.opt .layui-btn{height: 30px;line-height: 30px;background-color: #4fc0e8;border-radius: 0px;}
.ncap-form-default dd.opt .express-tag {margin-top: 10px;}
.ncap-form-default dd.opt .express-tag>span {font-size: 14px;font-weight: normal;line-height: 20px;color: #636669;padding: 1px 7px;border: 1px solid #e9edef;border-radius: 2px;margin-right: 10px;cursor: pointer;display: inline-block;}
.ncap-form-default dd.opt .express-tag>span.select-express {border: 1px solid #F60 !important;color: #F60 !important;}
.ncap-form-default dd.opt .selectPro_arg{ height: 30px;}
.ncap-form-default dt.tit em { font: bold 14px/20px tahoma, verdana; color: #F60; vertical-align: middle; display: inline-block; margin-right: 5px; margin-left: -14px; }
.ncap-form-default .input-txt { width: 374px !important; }
#web_name{ width:280px !important;}
.ncap-form-default ul.list { }
.ncap-form-default ul.list li { clear: both; }
.ncap-form-default .input-btn { font-size: 12px; background-color: #F5F5F5; vertical-align: top; display: inline-block; height: 26px; padding: 0 10px; border: solid 1px #D7D7D7; border-radius: 4px; cursor: pointer; }
.ncap-form-default .input-btn:hover { text-decoration: none; color: #333; background-color: #E6E6E6; border-color: #A9A9A9; }
.ncap-form-default p.notic { line-height: 12px; line-height: 18px; color: #AAA; margin-top: 4px; clear: both; display: none;}
.ncap-form-default dl.row:hover p.notic { color: #00B7EE; }
.notic2{ font-size: 12px; line-height: 20px;}
.hui{ padding-left: 10px;color: #C0C0C0;}
.ncap-form-default div.bot { display: block; padding: 12px 0 10px 135px; }
.ncap-form-default.help div.bot { padding: 12px 0 10px 0;}

.ncap-form-default .unified{padding: 0 20px;}
.ncap-form-default .normTbe{border-left:1px solid #eee;border-top:1px solid #eee; }
.ncap-form-default .normTbe td,.normTbe th{border-bottom: 1px solid #eee;border-right:1px solid #eee;padding: 10px;text-align: center;}
.ncap-form-default .normTbe th{background: #f4f6f8;}
.ncap-form-default .normTbe input{width: 80%;text-align: center;}
.ncap-form-default .hide{display: none;}
.ncap-form-default .copybtn{}

/*专题文档*/
.ncap-form-default .special-items{position: relative;margin: 20px auto;width:calc( 100% - 42px);padding: 20px;display:block;border: 1px solid #eee;border-radius: 4px;}
.ncap-form-default .special-items .special-del{position:absolute;top:10px;right:10px;display:block;background-color:#f4f4f4;border:1px solid #eee;color:#666;padding:2px 4px;border-radius:3px;}
.ncap-form-default .special-items .special-del:hover{color:#fff;background-color:#f66;border:1px solid #f66;cursor:pointer}
.ncap-form-default .special-items .special-tags{position:absolute;top:10px;right:70px;display:block;background-color:#f4f4f4;border:1px solid #eee;color:#666;padding:2px 4px;border-radius:3px;}
.ncap-form-default .special-items .special-tags:hover{color:#fff;background-color:#3aa8cf;border:1px solid #3aa8cf;cursor:pointer}
.ncap-form-default .special-item{display:inline-block;margin-bottom:10px;margin-right:10px}
.ncap-form-default .special-items-row{width:100%;display:inline-block;margin:5px auto}
.ncap-form-default .special-items-row .option-item{display:inline-block;margin-right:15px}
.ncap-form-default .special-items .special-item-l{display:block;float:left;text-align:right;min-width:80px;padding-left:34px;line-height:30px;margin-right:15px}
.ncap-form-default .special-items .special-item-l em { font: bold 14px/20px tahoma, verdana; color: #F60; vertical-align: middle; display: inline-block; margin-right: 5px; margin-left: -14px; }
.ncap-form-default .special-items .special-item-r{display:block;float:left;line-height:30px}
.ncap-form-default .special-add{width:70px;padding:4px 16px;color:#fff;background-color:#f66;border-color:#f66;border-radius:3px}
.ncap-form-default .special-add:hover{cursor:pointer}

/*弹出框体下的特殊性*/
.dialog_content .ncap-form-default,
.dialog_content .ncap-form-all { width: 96%; margin: 0 auto; padding: 0; }
.dialog_content .ncap-form-default dt.tit { text-align: right; width: 20%; padding-right: 2%; padding-left: 0px;}
.dialog_content .ncap-form-default dd.opt { text-align: left; width: 76%; }
.dialog_content .ncap-form-all dl.row { padding: 8px 0; }
.dialog_content .ncap-form-all dt.tit { font-size: 12px; font-weight: 600; line-height: 24px; background-color: transparent; height: 24px; padding: 4px; }
.dialog_content .ncap-form-all dd.opt { font-size: 12px; padding: 0; border: none; }
.dialog_content .ncap-form-all .search-bar { padding: 4px; }
.dialog_content .bot { text-align: center; padding: 12px 0 10px 0 !important; }
.dialog_content .rule-goods-list { position: relative; z-index: 1; overflow: hidden; max-height: 200px; }
.dialog_content .rule-goods-list ul { font-size: 0; }
.dialog_content .rule-goods-list ul li { font-size: 12px; vertical-align: top; display: inline-block; width: 48%; padding: 1%; }
.dialog_content .rule-goods-list ul li img { float: left; width: 32px; height: 32px; margin-right: 5px; }
.dialog_content .rule-goods-list ul li a,
.dialog_content .rule-goods-list ul li span { color: #555; line-height: 16px; white-space: nowrap; text-overflow: ellipsis; display: block; float: left; width: 180px; height: 16px; overflow: hidden; }
.dialog_content .rule-goods-list ul li span { color: #AAA; }
/*权限组*/
.ncap-account-all { padding-left: 1%; }
.ncap-account-container { line-height: 20px; display: block; min-height: 20px; padding: 15px 0 10px 0; border-top: dotted 1px #CCC; }
.ncap-account-container:nth-child(even) { background: #FDFDFD; }
.ncap-account-container:hover { background: #F4FCFA; }
.ncap-account-container h4 { font-size: 12px; font-weight: normal; color: #777; text-align: right; vertical-align: top; display: inline-block; *display: inline/*IE7*/;
width: 11%; margin-right: 1%; *zoom: 1;
}
.ncap-account-container-list { font-size: 0; *word-spacing:-1px/*IE6、7*/;
vertical-align: top; display: inline-block; *display: inline/*IE7*/;
width: 86%; padding-left: 1%; border-left: dotted 1px #CCC; *zoom: 1;
}
.ncap-account-container-list li { font-size: 12px; line-height: 20px; color: #999; vertical-align: top; letter-spacing: normal; word-spacing: normal; display: inline-block; *display: inline/*IE7*/;
width: 150px; height: 20px; margin-bottom: 5px; *zoom: 1;
}
/*运单模板设计微调*/
.ncap-waybill-list { }
.ncap-waybill-list li { width: 19%; padding: 0 1% 8px 0; }
.ncap-waybill-list li i { cursor: pointer; }
/*内容页面样式
------------------------------------------------------------------- */
.page_header{background-color:#fff;padding: 20px 15px 20px 23px;text-align: left; overflow: hidden;margin-bottom: 10px;}
.flex-row{display: flex;   flex-direction: row;align-items: center;}
.flex-column{display: flex; flex-direction: column;justify-content: flex-start;margin-left: 30px;margin-right: 90px;width: 25%;}
.f-16{font-size: 16px;}   .f-20{font-size: 20px;} .f-28{font-size: 28px;}
.col-6{color: #666;} .m-b20{margin-bottom: 20px;}
.line{width: 1px;height: 110px;background-color: #f5f5f5;}
.span_height{height:40px;white-space: nowrap;}
.page { background-color:#fff;padding: 15px 15px 30px 15px;text-align: left; overflow: hidden; }
.fixed-bar { background-color: #FFF; width: 100%; padding-bottom: 4px; z-index: 99; top: 0; left: 0;}
.item-title { line-height: 24px; white-space: nowrap; width: 100%; padding-top: 3px;  border-bottom: solid 1px #eee;  margin-bottom: 12px;}
.item-title .subject { vertical-align: bottom; display: inline-block; *display: inline;
*zoom: 1;height: 26px; padding: 6px 0;}
.item-title h3 { font-size: 16px; font-weight: normal; line-height: 20px; color: #333; display: inline-block;}
.item-title h5 { font-size: 12px; font-weight: normal; line-height: 18px; color: #777; display: inline-block;margin-left: 10px }
.tab-base { vertical-align: bottom; display: inline-block; *display: inline;
*zoom: 1;
}
.tab-base li { height: 43px; }
.tab-base a { line-height: 20px; color: rgba(0,0,0,.65) ;  display: block;  height: 20px;padding: 11px 16px ;  margin-left: -1px ; cursor: pointer;font-size: 15px;position: relative; }
.tab-base a:after {content: ' ';width: 0;height: 2px;background: #3398cc;display: block;z-index: 10;transition: width .1s ease-in-out;position: absolute;bottom: -2px;left: 50%;transform: translate(-50%,0);}
.tab-base li:first-child a{ margin-left: 0px !important;}
.tab-base a:hover { }
.tab-base a.current,
.tab-base a:hover.current {cursor: default !important;color: #0070CC !important;}
.tab-base a.current:after,
.tab-base a:hover.current:after {width: 20px;}
.item-title a.back { display: inline-block; vertical-align: bottom; margin: 0 0px 8px 0;cursor: default;
    background-image: -webkit-gradient(linear,left top,left bottom,from(#fff),to(#F8F8F8));
    background-image: -webkit-linear-gradient(top,#fff 0,#F8F8F8 100%);
    background-image: -o-linear-gradient(top,#fff 0,#F8F8F8 100%);
    background-image: linear-gradient(to bottom,#fff 0,#F8F8F8 100%); padding:0 8px; float: right; color: #333;border: 1px solid #AAA;}
.item-title a.back:hover {background-image:-webkit-gradient(linear,left top,left bottom,from(#f2f2f2),to(#ebebeb));background-image:-webkit-linear-gradient(top,#f2f2f2 0,#ebebeb 100%);background-image:-o-linear-gradient(top,#f2f2f2 0,#ebebeb 100%);background-image:linear-gradient(to bottom,#f2f2f2 0,#ebebeb 100%); color:#222; }
.item-title a.back i { font-size: 40px; }
.item-title a.back_xin { display: inline-block;margin: 0 0px 8px 0;cursor: pointer;padding: 0 8px;color: #333;}
.item-title a.back_xin:hover {cursor: pointer;}
.item-title a.back_xin i { font-size: 14px;color: #5d5d5d;}
.item-title a.back_sz { display: inline-block; vertical-align: bottom; margin: 4px 0px 8px 0;cursor: pointer; padding:0 8px; float: right; color: #333;}
.item-title a.back_sz i { font-size: 18px;color: #3398cc;}
.item-title a.return {margin-right: 0;font-size: 16px;font-weight: 400;color: #373737;cursor: pointer;position: relative;}
.item-title a.return i{font-size: 16px;margin-right: 4px;color: #5c5c5c;}
.item-title a.tit_z {height: 43px;line-height: 43px;font-size: 18px;color: #373737;cursor: pointer;position: relative;}
/*注释说明帮助*/
.explanation { color: #0ba4da !important; background-color: rgba(79, 192, 232, 0.11) !important; display: block; width: 99%; height: 100%; padding: 6px 9px; position: relative; overflow: hidden; }
/*.explanation:before{content: "";background-image: url(../images/wave.png); width: 100%;height: 100%;position: absolute;top: 0px;left: 0px;border-radius: 5px;background-repeat: no-repeat;background-size: cover;}*/
.explanation .title { white-space: nowrap; margin-bottom: 8px; position: relative; cursor: pointer; }
.explanation .title h4 { font-size: 14px; font-weight: normal; line-height: 20px; height: 20px; display: inline-block; }
.explanation .title i { font-size: 18px; vertical-align: middle; margin-right: 6px; }
.explanation .title span { background: url(../images/combine_img.png) no-repeat -580px -200px; width: 20px; height: 20px; position: absolute; z-index: 1; top: -6px; right: -9px; }
.explanation ul { color: #748A8F; margin-left: 10px; }
.explanation li { line-height: 20px; background: url(../images/macro_arrow.gif) no-repeat 0 10px; padding-left: 10px; margin-bottom: 4px;  }
/* 宽度\高度\尺寸
------------------------------------------------------------------- */
.w18pre { width: 18%; }
.size-64x64 { width: 64px; height: 64px; }
.size-88x29 { width: 88px; height: 29px; }
.size-72x72 { width: 72px; height: 72px; }
.size-106x106 { width: 106px; height: 106px; }
.red { color: red; }
.blue { color: #06C;}
.grey { color: #999999!important;cursor: no-drop!important; }
.no-grey{ color: #999999!important;}
.grey:hover {text-decoration: none!important;}
.orange { color: #F60; }
.bold { font-weight: bold; color: #545454 }
/* tip提示 */
.tip-yellowsimple { color: #000; background-color: #fff9c9; text-align: left; min-width: 50px; max-width: 300px; border: 1px solid #c7bf93; border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px; z-index: 1000; padding: 6px 8px; }
.tip-yellowsimple .tip-inner { font: 12px/16px arial, helvetica, sans-serif; }
.tip-yellowsimple .tip-arrow-top { background: url(../images/tip-yellowsimple_arrows.gif) no-repeat; width: 9px; height: 6px; margin-top: -6px; margin-left: -5px; top: 0; left: 50%; }
.tip-yellowsimple .tip-arrow-right { background: url(../images/tip-yellowsimple_arrows.gif) no-repeat -9px 0; width: 6px; height: 9px; margin-top: -4px; margin-left: 0; top: 50%; left: 100%; }
.tip-yellowsimple .tip-arrow-bottom { background: url(../images/tip-yellowsimple_arrows.gif) no-repeat -18px 0; width: 9px; height: 6px; margin-top: 0; margin-left: -5px; top: 100%; left: 50%; }
.tip-yellowsimple .tip-arrow-left { background: url(../images/tip-yellowsimple_arrows.gif) no-repeat -27px 0; width: 6px; height: 9px; margin-top: -4px; margin-left: -6px; top: 50%; left: 0; }
/* table
------------------------------------------------------------------- */
#gcategory select { margin-right: 3px; }
/* tb */
.rowform { width: 306px; overflow: auto; }
.rowform .txt,
.rowform textarea { margin-right: 10px; width: 250px; }
.rowform .txt2,
.rowform textarea { margin-right: 10px; width: 110px; }
.rowform select { margin-right: 10px; width: 256px; }
.rowform .class-select { width: 90px; margin: 0; }
.rowform .change-select-2 select { width: 123px; } /*2级联动选择*/
.rowform .change-select-3 select { width: 78px; } /*3级联动选择*/
.rowform .radio { margin-top: -2px !important; *margin-top:0 !important;
*margin-top:-2px;
}
.rowform li { overflow: hidden; float: left; margin-right: 10px; white-space: nowrap; cursor: pointer; }
.rowform .clear { clear: both; float: none; margin-bottom: 10px; }
.rowform .nofloat { clear: both; }
.rowform .nofloat li span.radio { line-height: 25px; width: 100px; float: left; }
.rowform .nofloat li select { width: 156px; }
.rowform .nofloat li.left { float: left; }
.rowform .nofloat li { float: none; margin: 5px 0; overflow: visible; }
.ckbox { width: 700px; }
.ckbox li { float: left; margin: 5px 10px 5px 0; white-space: nowrap; width: 130px; height: 20px; }
/* Page title
---------------------------------------------------------------------*/

.txt,
.txt2,
.select { height: 20px; line-height: 20px; }
tr td.handler span { display: block; width: 140px; text-align: left; margin: 0 auto; }
tr.no_data td { font-size: 14px; line-height: 120px; color: #09C; text-align: center; font-weight: bold; }
.stat { float: left; height: 20px; line-height: 20px; color: #a3a3a3; text-decoration: none; }
.select { width: 370px; color: #444; font-size: 12px; }
.wordSpacing5 { word-spacing: 5px; }
.text250 { width: 238px; color: #444; font-size: 12px; padding-left: 18px }
.normal { font-weight: normal; }
.file { width: 330px; }
.floatleft { float: left; padding-left: 15px; }
.clear { clear: both; }
.mt10 { margin-top: 10px; }
.mr10 { margin-right: 10px; }
.sort_order { width: 50px; height: 17px; line-height: 17px; text-align: center }
.order th { border-top: 1px dotted #CBE9F3; font-weight: 700; color: #000; }
.order .noborder th { border-top: none; }
.order .space th { font-size: 14px; padding: 0; }
.order td { }
.order ul { width: 98%; margin: 5px auto; overflow: hidden; }
.order ul li { color: #333; width: 50%; float: left; padding: 4px 0; }
.order ul li strong { font-weight: normal; color: #888; padding: 0 6px 0 0; }
.order .goods { border: solid 1px #CBE9F3; width: 98%; margin: 10px auto; }
.order .goods th { background-color: #F3FBFE; }
.red_common { font-weight: bold; color: #ff5400; }
.red_big { font-weight: bold; color: #ff5400; font-size: 16px; }
form label.error { font-style: normal; font-weight: normal; color: #E84C3D; margin-left: 5px; }
form label.error i { font-size: 14px; margin-right: 4px; }
form input.error,
form textarea.error { background-color: #FFF0F0; background-repeat: repeat; border: 1px dashed #E84C3D; }
/*投诉部分*/

.admin { color: black; }
.accuser { color: red; }
.accused { color: green; }
/* widget */
big,
.big { font-size: 120% !important; line-height: 120%; }
.checked,
.checked .txt { color: #0D0; }
.lightfont { color: #CCC; }
.light,
.light a { color: #AAA; }
.error { color: #F00; }
.nomargin { margin: 0 !important; }
.marginleft { margin-left: 20px; }
.marginright { margin-right: 10px; }
.margintop { margin-top: 10px; }
.marginbot { margin-bottom: 10px; }
.nobg,
.nobg td { background: none; }
.nobdb { border-bottom: none; }
.nobdt { border-top: none; }
.noborder,
.noborder td { border-bottom: 0; border-top: 0; }
.noborder td.tips { color: #999; vertical-align: middle; }
.noborder td.tips:hover,
.normalfont { color: #000; }
.tips a { color: #FFF; background-color: #F60; padding: 2px 4px; margin: 0 4px; border: 1px solid #F30; border-radius: 4px; -moz-border-radius: 4px; -webkit-border-radius: 4px; /*Firefox\Chrome\Safari\IE9\元素圆角效果*/ }
.vatop { vertical-align: top; }
.lineheight { line-height: 150%; }
.left { float: left; }
.right { float: right; }
.center { text-align: center; }
.alignleft { text-align: left; }
.alignright { text-align: right; }
.bold { font-weight: 700; }
.normal { font-weight: 400; }
.clear { clear: both; }
/* calendar */

.header,
.header td,
.header th { border-top: 1px dotted #DEEFFB; font-weight: 700; }
.thead th { font-size: 13px; font-weight: 700; color: #333; white-space: nowrap; border-top: solid 1px #DEEFFB; }
/* 外边距 Margin，三组从窄到宽，级别分别为：n, m, w */
.mtw { margin-top: 20px !important; }
/* Used for the Switch effect: */
.onoff { font-size: 0; position: relative; overflow: hidden; display: block; float: left; }
.onoff label { vertical-align: top; display: inline-block; *display: inline;
*zoom: 1;
cursor: pointer; }
.onoff input[type="radio"] { position: absolute; top: 0; left: -999px; }
.onoff .cb-enable
{ color: #777; font-size: 12px; line-height: 20px; background-color: #f8f8f8; height: 20px; padding: 1px 9px; border-style: solid; border-color: #ddd;border-radius:4px 0 0 4px; }
.onoff .cb-disable { color: #777; font-size: 12px; line-height: 20px; background-color: #f8f8f8; height: 20px; padding: 1px 9px; border-style: solid; border-color: #ddd;border-radius:0 4px 4px 0; }

.onoff .cb-enable { border-width: 1px 0 1px 1px; }
.onoff .cb-disable { border-width: 1px 1px 1px 0;  }
.onoff .cb-disable.selected { color: #FFF;background-color: #5FB878;border-color: #5FB878;border-radius: 0 4px 4px 0; }
.onoff .cb-enable.selected { color: #FFF; background-color: #5FB878;border-color: #5FB878;border-radius:4px 0 0 4px; }
/* Buttons
---------------------------------------------------------------------*/


.align-center { text-align: center; }
.nowrap { white-space: nowrap; }
.nobg { background: transparent none no-repeat scroll 0 0 !important; }
.space th { font-size: 16px; padding-left: 0 !important; }
table.search { margin: 12px 0 6px 4px; }
.yes-onoff a,
.no-onoff a,
.power-onoff a { line-height: 999%; background: url(../images/combine_img.png) no-repeat scroll; display: inline-block; width: 34px; height: 34px; overflow: hidden; }
.yes-onoff img,
.no-onoff img,
.power-onoff img { }
.yes-onoff a.enabled { background-position: 0px -300px; }
.yes-onoff a:hover.enabled { background-position: -40px -300px; }
.yes-onoff a:active.enabled { background-position: -80px -300px; }
.yes-onoff a.disabled { background-position: -120px -300px; }
.yes-onoff a:hover.disabled { background-position: -160px -300px; }
.yes-onoff a:active.disabled { background-position: -200px -300px; }
.no-onoff a.enabled { background-position: 0px -340px; }
.no-onoff a:hover.enabled { background-position: -40px -340px; }
.no-onoff a:active.enabled { background-position: -80px -340px; }
.no-onoff a.disabled { background-position: -120px -340px; }
.no-onoff a:hover.disabled { background-position: -160px -340px; }
.no-onoff a:active.disabled { background-position: -200px -340px; }
.power-onoff a.enabled { background-position: 0px -380px; }
.power-onoff a:hover.enabled { background-position: -40px -380px; }
.power-onoff a:active.enabled { background-position: -80px -380px; }
.power-onoff a.disabled { background-position: -120px -380px; }
.power-onoff a:hover.disabled { background-position: -160px -380px; }
.power-onoff a:active.disabled { background-position: -200px -380px; }
.msg .tip { line-height: 32px; color: #555; }
.msg .tip2 { line-height: 32px; color: #999; }

.color .colorPicker { vertical-align: middle; display: inline-block; float: none; }
.color .evo-pointer { vertical-align: middle; display: inline-block; width: 24px; height: 24px; float: none; margin-left: 8px; border-radius: 4px; }
.color .colorPicker,
.color .evo-pointer { *display: inline/*IE6,7*/;
}
/* Scrollbar jQuery Plugin
-------------------------------------- */
.ps-container .ps-scrollbar-x,
.ps-container .ps-scrollbar-y { background-color: #AAA; height: 8px; -webkit-border-radius: 4px; -moz-border-radius: 4px; border-radius: 4px; position: absolute; z-index: auto; bottom: 3px; opacity: 0; filter: alpha(opacity=0); -webkit-transition: opacity.25s linear; -moz-transition: opacity .25s linear; transition: opacity .25s linear; }
.ps-container .ps-scrollbar-y { right: 3px; width: 8px; bottom: auto; }
.ps-container:hover .ps-scrollbar-x,
.ps-container:hover .ps-scrollbar-y { opacity: .6; filter: alpha(opacity=60); }
.ps-container .ps-scrollbar-x:hover,
.ps-container .ps-scrollbar-y:hover { opacity: .9; filter: alpha(opacity=90); cursor: default; }
.ps-container .ps-scrollbar-x.in-scrolling,
.ps-container .ps-scrollbar-y.in-scrolling { opacity: .9; filter: alpha(opacity=90); }
/*商家入驻表单*/
table.type { width: 700px; border: solid 1px #EEE; }
table.type thead th { color: #555; background-color: #F7F7F7; text-align: center; padding: 4px; border-color: #EEE; }
table.type tbody td { color: #777; text-align: center; padding: 4px; border-color: #EEE; }
table.type tbody td input { width: 60px; padding: 0; }
/* 翻页样式 */
.pagination { text-align: center; display: block; margin: 0!important; }
.pagination ul { font-size: 0; *word-spacing:-1px/*IE6、7*/;
display: inline-block; *display: inline/*IE6,7*/;
margin: 0 auto!important; padding: 0; zoom: 1; }
.pagination ul li { vertical-align: middle; letter-spacing: normal; word-spacing: normal; display: inline-block; width: auto!important; height: auto!important; padding: 0!important; margin: 0 !important; border: none !important; }
.pagination ul li {
*display: inline/*IE6,7*/;
*float: left; zoom: 1; }
.pagination li span { font-size: 12px; line-height: 24px; color: #AAA; list-style-type: none; background-color: #F5F5F5; display: block; height: 24px; padding: 0px 8px; margin: 0px; border: 1px solid; border-color: #DCDCDC #DCDCDC #B8B8B8 #DCDCDC; }
.pagination li:first-child span { border-radius: 4px 0 0 4px; }
.pagination li:last-child span { border-radius: 0 4px 4px 0; }
.pagination li a span,
.pagination li a:visited span { color: #333; text-decoration: none; cursor: pointer; }
.pagination li a:hover { text-decoration: none; }
.pagination li a:hover span,
.pagination li a:active span { color: #333; background-color: #E8E8E8; border-color: #D0D0D0 #D0D0D0 #AEAEAE #D0D0D0; cursor: pointer; }
.pagination li span.currentpage { color: #FFF; font-weight: bold; background-color: #41BEDD; border-color: #3AABC6 #3AABC6 #318EA6 #3AABC6; border-radius: 0; }
.pagination>.disabled>a, 
.pagination>.disabled>a:focus, 
.pagination>.disabled>a:hover {
    cursor:not-allowed!important;
}
.paixv a { text-decoration: none; color: #000 }
.paixv a:hover { color: #06F }
.hDiv::-webkit-scrollbar{display:none;}
span.err{color:#F00; display:none;}

.eylogo {
    padding-top:5px;
}
.eylogo a {
    height: 60px;
    line-height: 60px;
    padding-left: 13px;
    text-align: left;
}
.eylogo a img {
    height: 45px;
    vertical-align: middle;
}
.eycms_cont_left{ float:left; width:166px; position:relative; background:#3398cc; color:#fff; z-index:99;height:100%;font-size:14px; }
.eycms_cont_left a{ color:#fff;font-family: 微软雅黑;}
.eycms_cont_left dl{padding:10px 0; position:relative;border-bottom: 1px solid rgba(255,255,255,0.1);}
.eycms_cont_left dl:nth-child(2){height: calc(100% - 90px);}
.eycms_cont_left dl:last-child,.eycms_cont_left dl:first-child{border-bottom: none;}
.eycms_cont_left dl dt{ padding:15px 25px 5px; color:#e7e7e7;}
.eycms_cont_left dl dd{height: 100%;overflow: auto;overflow-y: auto;}
.eycms_cont_left dl dd::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  }
.eycms_cont_left dl dd::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius   : 10px;
  background-color: #4a4a4b3d;
  background-image: -webkit-linear-gradient(
      45deg,
      transparent 100%,
      transparent
  );
  }
.eycms_cont_left dl dd::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background   :unset;
  border-radius: 10px;
  }
.eycms_cont_left dl dd a{ display:block; overflow: hidden;vertical-align: middle;  outline:none; padding-left:15px; height:42px; line-height:40px;transition:all .3s;-webkit-transition:all .3s;-moz-transition:all .3s;-o-transition:all .3s  }
.eycms_cont_left dl dd a i,.eycms_cont_left dl.jslist dt i{ margin-right:8px; width:28px; text-align:center; color:#fff; position:relative; top:2px; font-size:18px;display: inline-block; }
.eycms_cont_left dl dd a:hover,.eycms_cont_left dl dd a.on,.eycms_cont_left dl dt.on{ background:#2189be;  }
.eycms_cont_left dl.on{}
.eycms_cont_left dl dd a:active{ background:#2189be; }
.eycms_cont_left dl.jslist{ padding:0px 0;transition:all .3s;-webkit-transition:all .3s;-moz-transition:all .3s;-o-transition:all .3s}
.eycms_cont_left dl.jslist dt{ padding:0px; padding-left:15px; height:42px; line-height:40px; color:#fff; cursor:pointer; position:relative;overflow: hidden; }
.eycms_cont_left dl.jslist dt a{ display:block; }
.eycms_cont_left dl.jslist dd{ display:none; position:absolute; left:166px; bottom: 0px; background:#3398cc; width:166px;}
.eycms_cont_left dl:nth-child(2) dd dl.jslist dd{ top: 0px !important; bottom: auto !important; }
.eycms_cont_left dl.jslist dt i.fa-angle-right{ position:absolute; font-size:20px; color:#fff; right:0px;  height:40px; line-height:40px; }
.eycms_cont_left dl.jslist dd a:hover,.eycms_cont_left dl.jslist dd a.on{ background:#2189be;}
.eycms_cont_left dl.jslist:hover{ background:#2189be;}
.eycms_cont_left dl.jslist:hover dd{ display:block !important}
i.fa-mobile{ font-size:26px!important; top:4px!important;}
.eycms_cont_left dl i.fa-plus{ color:#71fdff!important;}
/*.eycms_cont_left dl i.fa-cube{ color:#ff80ad!important;}*/

.fixedbar { zoom: 1;text-align: center; }
.fixedbar .fixedbar-box{padding:10px 0px 10px 20px;text-align: left;}
.fixedbar .fixedbar-box .left{position: relative;
    left: -2.5px;color: #a7c6dc;}
.fixedbar .fixedbar-box .right{position: relative;
    left: 2.5px;color: #a7c6dc;}
.fixedbar:after { content: " "; display: block; clear: both; height: 0; }
.fixedbar a { display: inline-block;  padding:0px 5px; color: #a7c6dc; text-align: center;  transition: all .23s ease-out; }
.fixedbar a .fa { display: block; font-size: 20px; }
.fixedbar a span { font-size: 14px; }
.fixedbar a:hover {color: #eee; }
.fold .hidden-xs{ display: none; }

 .ey-tool .dropdown-toggle {
    border: 0px;
    cursor: pointer;
        margin-right: 18px;
    font-size: 14px;    color: #333;
}
.ey-tool i{margin-right:2px;}
.ey-tool i.fa-globe{font-size:14px;position:relative;}
.ey-tool i.fa-bell-o{font-size:17px;position:relative;top:1px;left:3px;}
.ey-msecount-tool span.label{position:absolute;right:8px;top:5px;border-radius:59px;height:16px;line-height:16px;font-size:12px;padding:0px 5px;font-weight:normal;}
.ey-tool li.ey-tool-list{margin:5px 8px;color:#666;}
select{ box-shadow: none; marign:0px !important; }
.c_art_title div{ white-space: normal !important; }
.item-title a.back i { font-size: 14px; }
.ncap-form-default dd.opt a.ui_tips{background: url(../images/ui_tip.png) no-repeat; opacity: 0.5; background-size: 100%; display: inline-block; text-indent: -1920px; width: 15px; height: 21px; vertical-align: bottom; margin-left: 3px; }
.ncap-form-default dd.opt a.ui_tips-ganhan{background: url(../images/ui_tip_l.png) no-repeat; opacity: 0.5; background-size: 100%; display: inline-block; text-indent: -1920px; width: 15px; height: 21px; vertical-align: bottom; margin-left: 3px; }
.ncap-form-default .users-form-item .users-form-item-content a.ui_tips{background: url(../images/ui_tip.png) no-repeat; opacity: 0.5; background-size: 100%; display: inline-block; text-indent: -1920px; width: 15px; height: 21px; vertical-align: bottom; margin-left: 3px; }
.checkboxall{ height: 28px; line-height: 44px; }
.zzbaidu .ui_tips  { display: none !important;; }
.zzbaidu .notic{ display: block !important; }

.ui-web_name .ui_tips,.ui-web_title .ui_tips,.ui-keyword .ui_tips,.ui-web_description .ui_tips  { display: inline-block !important; }

.dropdown-menu {
    position: absolute;
    top: 26px;
    right: 16px;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    text-align: left;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #ccc;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
}
.dropdown-menu>li>a {
    display: block;
    padding: 3px 20px;
    clear: both;
    font-weight: 400;
    line-height: 1.42857143;
    color: #333;
    white-space: nowrap;
    text-align: center;
}
.dropdown-menu>li>a:focus,.dropdown-menu>li>a:hover{color:#262626;text-decoration:none;background-color:#f5f5f5}
em.open{ position: relative; }
em.open>.dropdown-toggle.btn-default {
    color: #333;
    background-color: #e6e6e6;
    border-color: #adadad;
}

em.open .dropdown-toggle {
    -webkit-box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
    box-shadow: inset 0 3px 5px rgba(0,0,0,.125);
}

.btn {
    display: inline-block;
    padding: 6px 12px;
    margin-bottom: 0;
    font-size: 14px;
    font-weight: 400;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
}

em.open .dropdown-menu{ display: block; }
.btn-primary {
    color: #fff;
    background-color: #3398cc;
    width: 100%;
}


.w40x {
    width: 20px;
    height: 31px;
    float: left;
    position: relative;
    margin-right: 5px;
}

.w40x:before {
    content: '';
    position: absolute;
    top: -15px;
    left: 50%;
    width: 1px;
    height: 32px;
    background: #ccc;
}

.w40x:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 50%;
    height: 1px;
    background: #ccc;
}
.w40xc{ margin-right: 25px }
.w40xc:after{ width: 150%;  } 
.w40xc2:after{ width: 150%;  } 
.w40xc3:after{ width: 270%;  } 
.w40xc4:after{ width: 400%;  } 
.w40xc5:after{ width: 525%;  } 
.w40xc6:after{ width: 650%;  } 
.w40xc7:after{ width: 775%;  } 
.w40xc8:after{ width: 900%;  } 
.w40xc9:after{ width: 1025%;  } 
.w40xc10:after{ width: 1150%;  } 

img.layer_tips_img {
    width: 150px;
    max-height: 250px;
}

.flexigrid .mDiv .fbutton div.add{padding: 4px 16px 4px 12px;color: #fff;background-color: #3091f2; border: none;}
.flexigrid .mDiv .fbutton div.add:hover{ background-color: #49a9ee;}
.flexigrid .mDiv .fbutton{margin-left: 0; margin-right: 10px;}
.flexigrid .mDiv .fbutton div.add i{ margin-right:2px; font-size: 12px;}
.flexigrid .mDiv .fbutton div.adds{padding: 3px 16px 4px;color: #3091f2;background-color: #ffffff; border: 1px solid #3091f2;}
.flexigrid .mDiv .fbutton div.adds:hover{ background-color: #3091f2;color: #fff;}
.flexigrid .mDiv .fbutton div.adds i{ margin-right:2px; font-size: 12px;}

.flexigrid .mDiv .ftitle_nav .fbutton{margin-left: 0;margin-right: 10px;}
.flexigrid .mDiv .ftitle_nav .fbutton div{font-size: 13px;height: 22px; line-height: 22px;color: #555;border: unset;background-color: #F7F7F7;padding: 3px 9px;}
.flexigrid .mDiv .ftitle_nav .fbutton div span{background: #fff;padding: 3px 5px;margin-left: 7px;}
div.addartbtn{ border:none !important; font-size: 12px; }
.flexigrid .mDiv .ftitle_nav .fbutton div.cur{color: #3398cc;background-color: #ecf9ff;}
.flexigrid .mDiv .ftitle_nav .fbutton div.cur span{color: #3398cc;}


.addartbtn input.btn{ color: #333;background-color: #fff;border:none; height: 30px !important;  font-size: 12px; padding:0;  } 
.addartbtn input.btn:hover{background-color:none !important;border-bottom: 1px solid #0070CC ;}
.addartbtn input.btn.selected{background: none !important;color: #0070CC ;border-bottom: 1px solid #0070CC ;}

.addartbtn input.btn2{ color: #fff;background-color: #FF6666;border:1px solid #FF6666 !important; height: 28px !important;} 
.addartbtn input.btn2:hover{background-color: #FF534D !important;}
.addartbtn input.btn2.current{background: #e7e7e7;color: #000; border:none !important;}
.addartbtn input.btn2.current:hover{ background: #FF6666 !important; color:#fff; /* cursor: default; */ }

.addartbtn .sprt {
    color: #999;
    padding: 6px 12px;
    font-size: 12px;
    display: inline-block;
}

div.probtn{ float: left; margin-right: 6px; }
div.probtn input.btn{ color: #fff;background-color: #25C6FC; border:none !important;border:1px solid #25C6FC !important;}
div.probtn input.btn:hover{background-color: #3f6fb6 !important;}

a.upimg{}
a.upimg:hover{ cursor: move;}

.item-title .subject i{ float: left; font-size: 24px; color: #999; margin-right: 6px; }

.atta .ncap-form-default dt.tit{ width: 134px; padding-left: 0px; }

.other .flexigrid .bDiv td div img{ float: none; }
.flexigrid .bDiv td div i.arctotal{font-size: 12px;font-family: 宋体;color: #999;}
.flexigrid .bDiv td div.pb0{ padding-bottom: 0px; }

.form-horizontal .ncap-form-default div.bot{ /* padding-bottom: 128px; */padding-top: 0px;}
.form-horizontal .ncap-form-default dl.row{ border-bottom: none; }
/*基本信息-开发模式*/
.system-web{background-color: #F4F4F4;padding: 15px; overflow: auto;}
.system-web .ncap-form-default dd.opt{width: 60%;}
.system-web .variable{ float: right; width: 200px; }
.system-web .variable div {width: 60%; float: left; height: 28px; font-size: 12px; text-align:center; line-height: 28px; }
.system-web .variable div p{}
.system-web .variable div.r{ width: 40% }


/*管理员-新增角色*/
.admin_poplistdiv {
    background: #f5f5f5;
    padding: 5px 8px;
    border: 1px solid #dfdfdf;
    margin: 0px 0px 8px;
}
.admin_poplistdiv h2 {
    font-size: 14px;
    margin-bottom: 5px;
    cursor: default;
}
.arctype_child {
    margin: 5px 0px 3px;
    padding: 5px 8px;
    background: #fff;
    border: 1px solid #dfdfdf;
}

.rolecss{/* background-color: #FFF; */ overflow: auto; min-width: auto;}
.rolecss input{vertical-align: text-bottom;margin-bottom: 3px;}

  .rolecss  .table_box {
        border: 1px solid #e4e4e4;
    }
   .rolecss  .table_box td {
        border-right: 1px solid #e4e4e4;
        border-bottom: 1px solid #e4e4e4;
        line-height: 38px;
    }
   .rolecss  .table_box td ul {
        margin-top: 5px;
    }
   .rolecss  .layui-this-selected{
        background-color: #cbe9f3;
    }
    .rolecss .arctype_child{display: none;}
    .rolecss .arctype_bg{width: 13px;height: 13px;display: inline-block;margin:0 1px;cursor: pointer;}
    .rolecss .expandable{background: url(../images/tv-expandable.gif)}
    .rolecss .collapsable{background: url(../images/tv-collapsable-last.gif)}

.rolecss span.button.center_docu {
    background-position: -56px -18px;
}
.rolecss span.button.switch {
    width: 18px;
    height: 18px;
    margin-top: -6px;
}
.rolecss span.button {
    line-height: 0;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
    background-color: transparent;
    background-attachment: scroll;
    background-image: url(../../../plugins/ztree/css/zTreeStyle/img/zTreeStandard.png);
    margin: 0px;
    border-width: 0px;
    border-style: none;
    border-color: initial;
    border-image: initial;
    outline: none;
    background-repeat: no-repeat;
}
.rolecss .admin_poplistdiv label{ margin-right: 3px; }
.rolecss .admin_poplistdiv .arctype_child1{padding-left:10px;}
.rolecss .admin_poplistdiv .arctype_child2{padding-left:10px;color: #390; height:20px;}
.rolecss .form-horizontal .ncap-form-default div.bot{position: fixed;background: #fff; height: 36px;padding: 8px 0px 20px 134px;width: 100%; bottom: 0px;}
.rolecss .ncap-form-default{ padding-bottom: 58px; }
.rolecss .ncap-form-default dl.row{ position: inherit; }

img.cboximg{height: 11px; width: 13px; padding-right: 2px;}

/*a  upload 上传input美化 */
.a-upload {
    padding: 4px 10px;
    height: 20px;
    line-height: 20px;
    position: relative;
    cursor: pointer;
    color: #333;
    background: #fafafa;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    display: inline-block;
    *display: inline;
    *zoom: 1;
    font-size:13px;
}

.a-upload  input {
    position: absolute;
    font-size: 100px;
    right: 0;
    top: 0;
    opacity: 0;
    filter: alpha(opacity=0);
    cursor: pointer
}

.a-upload:hover {
    color: #444;
    background: #eee;
    border-color: #ccc;
    text-decoration: none
}
/*会员中心 新增样式 2019.4.10*/
.txpic{ position: relative;width: 66px;  cursor: pointer; height: 66px; }
.txpic img{width: 100%;height: 66px;float: left;}
.txpic em{ height: 18px; position: absolute; bottom: 0;  float: left; line-height: 18px;display: block; width:100%; background:rgba(0, 0, 0, 0.5);  color: #fff; text-align: center;  font-size:12px; font-family: 宋体; }

/*图集上传 bug修正 2019.4.25*/
.ui-sortable-placeholder{ display: inline-block; }
.tab-pane .images_upload {
    float: left;
    width: 16%;
    margin-right: 2%;
    margin-bottom: 15px;
    display: inline-block;
    cursor: move;
}

.tab-pane .images_upload .ic{
    width: 100%;
    border: 1px solid #ddd;
    padding: 5px;
    position: relative;
    float: left;
    background: #fff;
    height:auto;
}
.tab-pane .images_upload .cover-bg{ background: #000; opacity: 0.5; width:100%; height: 100%; position: absolute; z-index: 2 }
.tab-pane .images_upload .icaction{position: absolute; top:50%; margin-top: -15px; text-align: center; width:100%; z-index: 111}
.tab-pane .images_upload .icaction span{ height: 28px; line-height: 28px; padding: 0 8px; background: #FF6666;  color: #fff; display: inline-block; cursor: pointer;}

.tab-pane .images_upload .upimg img{     
    vertical-align: middle;
    max-width: 100%;
    max-height: 140px;
}
.tab-pane .images_upload .ic img{
    position:absolute;
    top:50%;
    transform:translate(-50%,-50%);
    z-index: 1;
    left:50%;
}
.tab-pane .images_upload textarea{ 
    width:100%; 
    box-sizing: 
    border-box;
    margin-top: 7px;
    font-size: 13px;
    height: 50px;
    float: left;
    color: #999;
    border-color: #ddd;
    -webkit-appearance: none;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.065) inset;
    transition: all 1s cubic-bezier(0.175, 0.885, 0.32, 1) 0s;
}

.tab-pane .images_upload:last-child{
    display: none;
}
.tab-pane .table-bordered{ width:100%; }
.tab-pane a.upimg,.tab-pane div.upimg{ 
    height: 140px;
    background: #fafafa;
    overflow: hidden;
    text-align: center;
    position: relative;
    display: block;
}

.tab-pane .load_input{
   display: none;
}

.tab-pane .upimg .delect{
   position: absolute;
   top: 0;
   right: 0;
   z-index: 2;
   width: 0px;
   height: 0px;
   border-top: 30px solid rgba(0,0,0,0.5);
   border-left: 30px solid transparent;
}
.tab-pane .upimg .delect::after {
    position: relative;
    content: "×";
    font-size: 20px;
    color: white;
    top: -33px;
    right: 15px;
}

.tab-pane .operation a{
    text-align: center;
    color: #666;
    outline: none;
    width: 34%;
    float: left;
    font-size: 14px;

}
.tab-pane .operation a input{ display: inline-block; margin-right: 3px; vertical-align:middle; margin-top: -3px;}
a.imgupload{
    display: inline-block;
    padding: .5em 1em;
    vertical-align: middle;
    font-size: 1.0rem;
    font-weight: 400;
    line-height: 1.2;
    text-align: center;
    white-space: nowrap;
    border-radius: 0;
    cursor: pointer;
    outline: 0;
    -webkit-transition: background-color .3s ease-out,border-color .3s ease-out;
    transition: background-color .3s ease-out,border-color .3s ease-out;
    color: #fff;
    background-color: #3bb4f2;
    border-color: #3bb4f2;
}
.tab-pane .operation a label{
     cursor: pointer;
}
a.imgupload i{font-size: 1.0rem;}
a.imgupload:hover{ background-image: linear-gradient(160deg, #3bb4f2 20%,#008cff 80%);}


.img-upload{border:1px solid #ddd;width:136px;height:133px;position: relative;display: inline-block;float: left;}
.img-upload .y-line{position: absolute;width:50%;height:1px;background-color:#ccc;left: 25%;top:49%;z-index: 5;}
.img-upload .x-line{position: absolute;width:1px;height:50%;background-color:#ccc;left: 49%;top:25%;z-index: 5;}

.b-img-upload{float: left;width: 16%;margin-right: 2%;display: inline-block;min-height: 150px;}

 @media (max-width:1680px) {
.tab-pane .images_upload{width: 24%;}
.tab-pane .operation a{ font-size: 12px; }
.tab-pane .operation a label{cursor: pointer;}
}
div.tab-pane.pics a.upimg{ height: auto; padding: 0px;}
div.tab-pane.pics .images_upload{ width:auto;position: relative; }
div.tab-pane.pics .delect{ position: absolute;top:0;right: 0;z-index: 2;width: 0px;height: 0px;border-top:30px solid rgba(0,0,0,0.5);border-left:30px solid transparent; }
div.tab-pane.pics .delect::after {position: relative;content: "\d7";font-size: 20px;color: white;top: -33px;right: 15px;}

.template_div{margin: 5px 0px;}

/*后台 栏目管理-产品展示-商品规格 2019.7.23*/
.preset-bt{position: relative;border: 1px solid #eee;overflow: hidden;padding: 4px 6px;height: 20px;line-height: 20px;border-radius: 2px;display: inline-block;background-color: #f8f8f8;margin-bottom: 15px; }
.preset-bt span{ display: inline-flex;}
.preset-bt:hover{cursor:pointer;}
.preset-bt i{margin-left:6px;color:#b5b3b3;float: right;line-height: 20px;}
.preset-bt2{position: relative;border: 1px solid #eee;overflow: hidden;padding: 4px 6px;height: 20px;line-height: 20px;border-radius: 2px;display: inline-block;background-color: #f8f8f8;width: 150px;margin-bottom: 15px;float: left; }
.preset-bt2 i{color:#b5b3b3;line-height: 20px;right: 5px;position: absolute;}
.preset-bt2 i:hover{color:red;cursor:pointer;}
#SpecTempLateDiv td{color:#888;padding:10px!important;}
#SpecTempLateDiv table{border:1px solid #ddd; }
#SpecTempLateDiv table b{font-weight: normal;color: #333;}
#SpecTempLateDiv table b a{color: #0ba6f3;}
#SpecTempLateDiv input{border:1px solid #ddd;padding:4px}
.set-preset-bt{position: relative;overflow: hidden;display: inline-flex;margin-bottom: 15px;}
.set-preset-bt i:hover{color:red;cursor:pointer;}
.preset-bt3 span{position:relative;overflow:hidden;float: left;margin: 0 6px 6px 0}
.preset-bt3 i{position:absolute;top:7px;right:3px;color:#b5b3b3}
.preset-bt3 i:hover{color:red}
.set-preset-box{ margin-left: 10px;margin-right: 15px;width: 15px;height: 15px;position: relative;overflow: hidden;display: inline-flex;border: 1px solid #b8b9bd;border-top: none;border-right: none;float: left;}
.set-preset-con{display: flow-root;}
.set-preset-con .kuai{display: contents;}
.set-preset-con .abc{display: inline-flex;}
.set-preset-con .fox{display: inline;}
.preset-select{height: 30px;width: 164px;margin-bottom: 15px;display:inline-block;float: left; }
.set-preset-con .tongbu{line-height: 30px;cursor: pointer;float: left;display: inline-block;margin-left: 12px;}
.preset-bt-shuzi{position: relative;width:164px;overflow: hidden;display: inline-flex;margin-bottom: 15px;}
/*/*密码强度*/
.rank { border:none; background:url("../images/rank.gif") no-repeat; width:145px; height:22px; vertical-align:middle; cursor:default; margin:0 0 0 5px;}
.r0 { background-position:0 2; }
.r1 { background-position:0 -19px; }
.r2 { background-position:0 -40px; }
.r3 { background-position:0 -61px; }
.r4 { background-position:0 -82px; }
.r5 { background-position:0 -103px; }
.r6 { background-position:0 -124px; }
.r7 { background-position:0 -145px; }
div.bot2 {
    position: fixed;
    background: #b73939;
    height: 36px;
    padding: 10px 0px 10px 148px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    background: #fff;
    border-top: 1px solid #f1f1f1;
    z-index: 50;
}

.cate-dropup{
    position: relative;
    margin-right:4px;
    float: right;
}
.cate-dropup .cate-dropup-bt{
    width: 78px;
    height: 26px;
    line-height: 26px;
    border: 1px solid #eee;
    border-radius: 3px;
    padding: 2px 16px;
    background-color: #f9f9f9;
    font-size: 15px;
    cursor: pointer;
}
.cate-dropup .cate-dropup-bt i{
    margin-left:4px;
}
.cate-dropup .cate-dropup-con{
    position: absolute;
    top: 30px;
    display: block;
    width: 110px;
    height:auto;
    border: 1px solid #eee;
    overflow: hidden;
    background-color: #f9f9f9;
    padding: 10px 0;
    border-radius: 3px;
    display: none;
    border-top: none;
}
.cate-dropup .cate-dropup-con a{
    display: block;
    width: 100px;
    float: left;
    padding:6px 10px;
    color: #333;
    font-size: 15px;
}
.cate-dropup .cate-dropup-con a:hover{
    background-color: #3398cc;
    color: #fff;
}
/*顶部头像下拉*/
.admin_user_dropup{width: 50px;height: 42px;position: relative;display: inline-block;margin-right: 24px;}
.admin_user_dropup .admin_user_dropup_bt i{position: absolute;top: 10px;}
.admin_user_dropup .admin_user_dropup_bt img{width: 32px;height: 32px;border-radius: 50px;margin-right: 6px;}
.admin_user_dropup .admin_user_dropup_con{display: none; position: absolute;right: 0;top: 41px;min-width: 100%;line-height: 36px;padding: 0 0 5px;box-shadow: 0 2px 4px rgba(0,0,0,.12);background-color: #fff;z-index: 100;white-space: nowrap; }
.admin_user_dropup .admin_user_dropup_con a{padding: 0 14px;display: block;width: 70px;text-align: center;overflow: hidden;white-space: nowrap;text-overflow: ellipsis; color:#353535}
.admin_user_dropup .admin_user_dropup_con a:hover {background-color: #FAFAFA;}
.admin_user_dropup .admin_user_dropup_bt{position: relative;}
.admin_user_dropup .info-num{position: absolute;top: -4px;right:10px;z-index: 9;background-color: red;color: #fff;border-radius: 50px;font-size: 12px;padding: 0 2px; height:16px;line-height: 16px; min-width: 12px;text-align: center;}
.admin_user_dropup .admin_user_dropup_con li{position: relative;}
/*商城中心中间菜单*/
.sidebar-second {
    position: fixed;
    top: 0;
    left: 0;
    margin-right: 20px;
    width: 100px;
    height: 100%;
    background: #fff;
    color: #333;
    z-index: 99;

}
.sidebar-second .sidebar-second-title {
    height: 60px;
    line-height: 60px;
    padding-left: 20px;
    font-size: 15px;
}

.sidebar-second li a {
    color: #333;
    min-height: 40px;
    line-height: 40px;
    padding-left: 20px;
    display: block;
}

.sidebar-second li a:hover {
    background: #f5f5f5;
}

.sidebar-second li a.active {
    background: #f5f5f5;
}

/*小程序列表*/
.theme_box{
    margin: 0 auto 20px;
    /* overflow: hidden; */
    min-width: 1000px;
}

.theme_box .theme_list{
    float: left;
    display:inline-block;
    background-color: #fff;
    width: 100%;
    padding: 20px 0;
    text-align: left;
    overflow: hidden;
    box-shadow: 0 0 8px rgba(0,0,0,0.1);
    border-radius: 5px;
    margin-bottom: 20px;
}
.theme_box .theme_list .theme_content{
    padding: 0 20px;
}

.theme_box .theme_list .theme_logo{
    float: left;
    width: 110px;
    height: 110px;
    background-color: #eee;
}
.theme_box .theme_list .theme_logo img{
    width: 110px;
    height: 110px;
}
.theme_box .theme_list .theme_right{
    float: left;
    margin-left: 20px;
    height: 110px;
}
.theme_box .theme_list .theme_info_name{
    line-height:40px;
    font-size: 20px;
}
.theme_box .theme_list .theme_info{
    line-height: 30px;
}
.theme_box .theme_list .theme_info .theme_info_id{
    color: #999;
}
.theme_box .theme_list .theme_info span{
    margin-right: 20px;
}
.theme_box .theme_list .theme_info a:hover{
    color: #0062B2;
}
.theme_box .theme_list .theme_bottom{
    float: left;
    margin-top: 15px;
    width:100%;
}
.theme_box .theme_bottom .theme_bottom_con{
    margin: 0 auto;
    padding: 15px 0 0;
    display: block;
    border-top: 1px solid #eee;
    width:calc(100% - 40px); 
    overflow: hidden;
}
.theme_box .theme_bottom .theme_bottom_con a{
    display: block;
    float: left;
    height: 36px;
    line-height: 36px;
    background-color: #e6e6e6;
    border-radius: 50px;
    padding: 0 30px;
    margin-right: 20px;
    font-weight: bold;
    color: #9797A3;
    
}
.theme_box .theme_bottom .theme_bottom_con a:hover{
    background-color:#2ea8e6;
    color: #fff;
}

/*小程序模板列表2*/

.wx-tempalte .wx-tempalte-title{
    width: 100%;
    white-space: nowrap;
    display: block;
    overflow: hidden;
}
.wx-tempalte .wx-tempalte-titleLeft{
    float: left;
}
.wx-tempalte .wx-tempalte-titleLeft h3{
    padding: 0 10px;
    font-size: 15px;
    color: #333;
    border-left: 3px solid #2ea8e6;
    display: inline-block;
}
.wx-tempalte .wx-tempalte-titleLeft h5{
    display: inline-block;
}
.wx-tempalte .wx-tempalte-titleLeft .pReload {
    color: #999;
    width: 24px;
    height: 24px;
    line-height: 24px;
    margin: 0 0 0 10px;
    display: inline-block;
    cursor: pointer;
}
.wx-tempalte .wx-tempalte-titleRight{
    float: right;
}
.wx-tempalte .wx-tempalte-titleRight .cloud-tempalte-bt{
    display: block;
    padding: 6px 16px;
    color: #fff;
    background-color: #FF6666;
    border-color: #FF6666;
    border-radius: 3px;
    font-family: 宋体;
}
.wx-tempalte .wx-tempalte-nav{
    margin:0 auto;
    padding: 20px 0;
    overflow: hidden;
    width: 100%;
}
.wx-tempalte .wx-tempalte-nav li{
    float: left;
    margin: 10px 4px;
    width: 180px;
}
.wx-tempalte .wx-tempalte-nav li a{
    color: #666;
    border-radius: 2px;
    padding: 6px;
}
.wx-tempalte .wx-tempalte-nav .active{
    color:#2ea8e6;
}
.wx-tempalte .wx-tempalte-list{
    margin: 20px auto;
    font-size: 0;
}
.wx-tempalte .wx-template_wrap {
    position: relative;
    display: inline-block;
    vertical-align: top;
    margin: 0 40px 40px 0;
}
.wx-tempalte .wx-template_wrap:nth-child(5n+0){
    margin-right: 0;
}
.wx-tempalte .wx-template_wrap{
    width: 200px;
    height: 356px;
    display: inline-block;
    vertical-align: top;
    border-radius: 8px;
    -webkit-box-shadow: 0 2px 24px -4px rgba(0,0,0,.2);
    box-shadow: 0 2px 24px -4px rgba(0,0,0,.2);
    position: relative;
    -webkit-transition: -webkit-transform .3s ease,-webkit-box-shadow .3s ease;
    transition: -webkit-transform .3s ease,-webkit-box-shadow .3s ease;
    transition: transform .3s ease,box-shadow .3s ease;
    transition: transform .3s ease,box-shadow .3s ease,-webkit-transform .3s ease,-webkit-box-shadow .3s ease;
}
.wx-tempalte .wx-tempalte-item .preview_img {
    width: 100%;
    height: 100%;
    display: block;
    border-radius: 8px;
    position: relative;
}
.wx-tempalte .wx-template_wrap .code_layer {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,.4);
    border-radius: 8px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    display: none;
}
.wx-tempalte .wx-template_wrap .template_btn {
    margin: 0 auto;
    width: 145px;
    height: 34px;
    border-radius: 17px;
    font-size: 13px;
    text-align: center;
    line-height: 34px;
    cursor: pointer;
}
.wx-tempalte .wx-template_wrap .use_btn {
    background: #fff;
    color: #2ea8e6;
    margin-top: 150px;
}
.wx-tempalte .wx-template_wrap:hover {
    -webkit-box-shadow: 0 10px 20px -5px rgba(0,0,0,.25);
    box-shadow: 0 10px 20px -5px rgba(0,0,0,.25);
    -webkit-transform: translate3d(0,-8px,0);
    transform: translate3d(0,-8px,0);
}
.wx-tempalte .wx-template_wrap:hover .code_layer{
    display: block;
}

.wx-tempalte .wx-template_wrap .edit_btn{
    margin-top: 30px;
    background: #fff;
    color: #2ea8e6;
}
.wx-tempalte .wx-template_wrap .issue_btn{
    margin-top: 10px;
    background: #2ea8e6;
    color: #fff;
}
.wx-tempalte .wx-template_wrap .template_info{
    margin: 70px auto 0;
    padding: 10px 0;
    width: 160px;
    height: 90px;
    border-radius: 6px;
    font-size: 13px;
    text-align: center;
    line-height: 30px;
    background: #fff;
}
.wx-tempalte .wx-template_wrap .template_info .tempalte_oper a{
    margin: 0 4px;
    color: #2ea8e6;
}
.wx-tempalte .wx-template_wrap .template_info .tempalte_name{
    margin: 0 auto;
    font-size: 15px;
    width: 140px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
}
.wx-tempalte .wx-template_wrap .part-selected{
    position: relative;
    border: 1px dashed #4caf50;
}
.wx-tempalte .wx-template_wrap .part-selected span{
    position: absolute;
    bottom: 0;
    right: 0;
    width: 0;
    height: 0;
    border-bottom: 30px solid #4caf50;
    border-left: 30px solid transparent;
}
.wx-tempalte .wx-template_wrap .part-selected span:before,
.wx-tempalte .wx-template_wrap .part-selected span:after {
    content: '';
    pointer-events: none;
    position: absolute;
    top: 10px;
    right: -10px;
    color: black;
    border: 1px solid #fff;
    background-color: white;
}
.wx-tempalte .wx-template_wrap .part-selected span:before {
    width: 1px;
    height: 0px;
    left: -12px;
    top: 21px;
    transform: skew(0deg, 50deg);
}
.wx-tempalte .wx-template_wrap .part-selected span:after {
    width: 4px;
    height: 0px;
    left: -9px;
    top: 20px;
    transform: skew(0deg, -50deg);
}
.wx-tempalte .wx-tempalte-navson{
    padding:5px;
    background-color: #f4f4f4;
    overflow: hidden;
}
.wx-tempalte .wx-tempalte-navson li a {
    color: #666;
    border-radius: 2px;
    padding: 4px 10px;
}
.wx-tempalte .wx-tempalte-navson li {
    float: left;
    margin: 10px;
}
.wx-tempalte .wx-tempalte-navson .active{
    background-color:#2ea8e6;
    color: #fff;
}
/*插件列表*/
.plug-list{
    padding: 10px;
}
.plug-item-content{
    position: relative;
    display: inline-block;
    width: 32%;
    margin:0 1% 1.2% 0;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 1px 8px -2px rgba(0,0,0,.2);
    transition: -webkit-transform .3s ease,-webkit-box-shadow .3s ease;
    transition: transform .3s ease,box-shadow .3s ease;
    transition: transform .3s ease,box-shadow .3s ease,-webkit-transform .3s ease,-webkit-box-shadow .3s ease;
}
.plug-item-content:hover {
    box-shadow: 0 2px 10px 0px rgba(197,197,197,0.53);
    transform: translate3d(0,0,0);
}
.plug-item-content .plug-item-top{
    overflow: hidden;
    padding:10px;
}

.plug-item-content .plug-item-top .plug-img {
    float: left;
    display: block;
    margin-right: 10px;
    width: 80px;
    height: 80px;
}
.plug-item-content .plug-item-top .plug-img img{
    width: 80px;
    height: 80px;
}

.plug-item-content .plug-item-top .plug-text {
    display: flex;
    height: 84px;
    flex-direction: column;
    justify-content: center;
    text-align: left;
}

.plug-item-content .plug-item-top .plug-text .plug-text-title {
    color: #333;
    font-size: 16px;
    line-height: 24px;
    height: 24px;
}
.plug-item-content .plug-item-top .plug-text .plug-text-title .title-l{
    display: inline-block;
    width: calc(100% - 175px);
    overflow:hidden;
    text-overflow:ellipsis; 
    white-space:nowrap;
}
.plug-item-content .plug-item-top .plug-text .plug-text-title .title-r{
    width: 175px;
    float: right;
    font-size: 12px;

}
.plug-item-content .plug-item-top .plug-text .plug-text-title a{
    color: #333;
}
.plug-item-content .plug-item-top .plug-text .plug-text-title .icon-link_add{
    line-height: initial;
}
.plug-item-content .plug-item-top .plug-text .plug-text-des {
    font-size: 12px;
    color: #b2b2b2;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    height: 30px;
    overflow: hidden;
}
.plug-item-content .plug-item-top .plug-text .plug-text-des2 {
    font-size: 12px;
    color: #b2b2b2;
    overflow:hidden;
    text-overflow:ellipsis; 
    white-space:nowrap;
    height: 15px;
    overflow: hidden;
}
.plug-item-content .plug-item-top .plug-text .plug-text-versions{
    line-height: 22px;
    color: #777;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
}
.plug-item-content .plug-item-top .plug-text .plug-text-versions span{
    margin-right: 15px;
    font-size: 12px;
    
}
.plug-item-content .plug-item-bottm {
    position: relative;
    width: 96%;
    margin: 0 auto;
   color: #535353;
   font-size: 14px;
   height: 36px;
   line-height: 36px;
   text-align: center;
   border-top: 1px solid rgba(238,238,238,1);
}
.plug-item-content .plug-status{
    display: inline-block;
    font-size: 12px;
    color: #999;
    margin-left: 10px;
}

.plug-item-content .plug-status .yes {
    color: #1babf5;
    cursor: default;
    font-size: 14px;
}
.plug-item-content .plug-status .no {
    color: #9ea3a7;
    cursor:not-allowed;
    font-size: 14px;
}
.plug-item-content .plug-item-bottm a{
    margin:0 4px;
    display: inline-block;
    color: #1babf5;
}
.plug-item-content .plug-item-top .plug-price{
    margin-top: 4px;
    color: red;
    font-size: 14px;
    font-weight: bold;
    line-height: 22px;
}
/*配置接口右悬浮快捷导航 2020-04-07*/
.floatNav{
    position: fixed;
    display: block;
    right:30px;
    top:140px;
    width: 130px;
    min-height: 150px;
    background:#fff;
    z-index: 999;
    border: 1px solid #eee;
}
.floatNav .floatNav-tit{
    text-align: left;
    padding:0 6px 0 9px;
    height: 30px;
    line-height: 30px;
    border-bottom: 1px solid #e2e2e2;
    background: #f8f9fa;
    color: #999;
    font-size: 12px;
}
.floatNav .floatNav-con{
    width: 110px;
    position: relative;
    margin: 10px auto;
}
.floatNav .floatNav-con li{
    position: relative;
}
.floatNav .floatNav-con li .dot {
    position: absolute;
    z-index: 1;
    top: 0;
    left: -9px;
    width: 8px;
    height: 8px;
    border: 6px solid #fff;
    border-radius: 10px;
    background: #ddd;
}
.floatNav .floatNav-con li .event{
    position: relative;
    padding: 0 0 9px 12px;
    border-left: 2px solid #eee;
}
.floatNav .floatNav-con li:last-child .event{
    border: none;
}   
/*表单底部操作样式20200410*/
.footer-oper{
    width: 100%;
    position: relative;
    padding: 10px 0;
    border-bottom:1px solid #f7f7f7;
    box-sizing: border-box;
}
.footer-ope .divInput{
    text-align: center;
    display: inline-block !important;
}
        
.footer-oper .layui-btn-primary:hover{
    background-color: #fff;
}
.footer-oper .layui-btn-primary{
    border: 1px solid #ddd;
    background-color: #F7F7F7;
    line-height:28px;
    height: 30px;
    border-radius: 0;
}
        
.footer-oper .nav-dropup{
    position: relative;
    margin-left: 20px;
    margin-right: 5px;
    height: 32px;
    display: inline-block!important;
}
.footer-oper .layui-btn {
    padding: 0 10px;
    font-size: 12px;
}
.footer-oper .layui-form-checkbox[lay-skin=primary] {
    padding-left: 10px;
}
.footer-oper .nav-dropup i{
    font-size: 12px;
    margin-left: 4px;
}
.footer-oper .nav-dropup .dropdown-menus{
    position: absolute;
    bottom: 32px;
    left: 0;
    z-index: 99999;
    display:block;
    min-width: 87px;
    padding: 0;
    margin: .125rem 0 0;
    font-size: 12px;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.15);
    border-bottom:none;
    overflow: hidden;
}
.footer-oper .nav-dropup .dropdown-menus hr{ margin:5px 0;}
.footer-oper .nav-dropup .dropdown-menus a{
    display: block;
    width: 100%;
    padding: 8px 10px;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
}
.footer-oper .nav-dropup .dropdown-menus a:hover {
    color: #16181b;
    text-decoration: none;
    background-color: #f5f5f5;
}
    
/*开关面板20210517*/
.on-off_panel{
    position: relative;
    box-sizing: border-box;
    height:auto;
    width: 100%;
    overflow: hidden;
    transition: all .1s ease-out;
}
.on-off_content{
    position: relative;
}
.on-off_panel:hover {
}
.on-off_panel .title{
    position: relative;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    line-height: 22px;
    display: block;
    width: 100%;
    padding: 5px 0 5px 14px;
}
.on-off_panel .title::before{
    position: absolute;
    left: 0;
    top:50%;
    margin-top: -9px;
    content: "";
    display: inline-block;
    height:18px;
    width: 4px;
    background-color: #197971;
    border-radius: 2px;
}
.on-off_panel .title .close-bt{
    background: url(../images/combine_img.png) no-repeat -580px -200px;
    width: 20px;
    height: 20px;
    position: absolute;
    z-index: 1;
    top: -16px;
    right: -17px;
    cursor: pointer;
}
.on-off_panel .on-off_btns{
    margin-bottom: 30px;
    display: block;
    width: 100%;
}
.on-off_panel .on-off_btns ul{
    display: flex;
    flex-wrap: wrap;
}
.on-off_panel .on-off_btns ul li{
    margin:0 15px 10px 0;
    background-color: #fff;
    padding: 18px 20px;
}
.on-off_panel .on-off_btn{
    display: flex;
    align-items: center;
}
.on-off_panel .on-off_btn .on-off_btn_l p{
    font-size: 16px;
}
.on-off_panel .on-off_btn .on-off_btn_r .layui-form-switch{
    margin-top: 2px !important;
}
.on-off_panel .on-off_list{
    margin-bottom: 30px;
}
.on-off_panel .on-off_list ul{
    display: flex;
    flex-wrap: wrap;
}
.on-off_panel .on-off_list li{
    padding: 0 15px 15px 0;
    overflow: hidden;
    flex: none;
    width: 16.6666%;
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
}
.on-off_panel .on-off_list .marketing-nav li{
    width: 20%;
}
.on-off_panel .on-off_list ul.marketing-nav > li:nth-child(6n) {
  padding-right:15px;
}
.on-off_panel .on-off_list li a{
    display: flex;
    align-items: center;
    padding:10px;
    background-color: #ffffff;
    line-height: 60px;
    text-decoration: none;
    color: #333;
    transition: all 250ms linear;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 15px;
}
.on-off_panel .on-off_list li a span{
    margin-left: 10px;
    flex: 1;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 16px;
    line-height: 1.5;
}
.on-off_panel .on-off_list li a img{ width:60px;}
.on-off_panel .on-off_list li a:hover {
     box-shadow: 0 4px 13px -1px #EBEBEB;
}
.on-off_panel .on-off_list ul > li:nth-child(6n) {
  padding-right: 0px;
}
.on-off_panel .on-off_list li .icon{
    width: 56px;
    height: 56px;
    line-height: 56px;
    display: inline-block;
    text-align: center;
    background-color: #eee;
    border-radius: 50px;
}
.on-off_panel .on-off_list li .icon i{
    font-size: 30px !important;
    margin-right: 0 !important;
    color: #fff;
}
.on-off_panel .on-off_list li span>p{
    font-size: 12px;
    color: #999;
    display: inline;
}
.on-off_panel .on-off_list li span>h2{
     font-size: 16px;
      display: inline;
}

.on-off_panel .son-tit{
    display: block;
    line-height: 36px;
    margin-bottom: 10px;
    color: #666;
}
.on-off_panel .on-off_shade{
    position: absolute;
    height: 100%;
    width: 100%;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.6);
    z-index: 1;
    top:0;
    display: none;
}
.show-shade .on-off_shade{
    display: flex !important;
}
.on-off_panel .on-off_shadeCon{
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
    background-color: #FFFFFF;
    display: inline-block;
    height: auto;
    text-align: center;
    padding: 16px;
}
.on-off_panel .on-off_list.b-icon .more_li i{
    font-size: 20px;
    margin-right: 10px;
}
    
/*广告图片列表*/
.adpic{ }
.adpic li {position: relative; width:68px;max-height: 68px; margin:0 auto; vertical-align: middle; overflow: hidden;background-color: #f4f4f4;}
.adpic li img{ width:100%; min-height: auto;}
.adpic li span{position: absolute;bottom: 4px;right: 4px;color: #fff; background: rgba(0,0,0,0.5);height:16px;line-height: 16px;padding: 0 4px;border-radius: 4px;}
    
/*主题色选择样式*/

.ncap-form-default .theme-color li {
    display: inline-block;
    font-size: 0;
    cursor: pointer;
    padding: 1px;
    border: 1px solid #fff;
    margin-right: 6px;
    line-height: 1;
}
.ncap-form-default .theme-color li span {
    display: inline-block;
    width: 25px;
    height: 25px;
    overflow: hidden;
}

.ncap-form-default .theme-color .active {
    border: 1px solid #00b240;
    border-radius: 2px;
}

/*会员导航条*/
.member-nav-group{
    display: inline-block;
    line-height: 1;
    vertical-align: middle;
    font-size: 0;
}
.member-nav-group .member-nav-item{
    position: relative;
    display: inline-block;
    outline: none;
    font-size: 14px;
}
.member-nav-group .member-nav-item .btn{
    display: inline-block;
    line-height: 1;
    white-space: nowrap;
    vertical-align: middle;
    background: #fff;
    border: 1px solid #DCDFE6;
    font-weight: 500;
    border-left: 0;
    color: #606266;
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    position: relative;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 0;
}
.member-nav-group .member-nav-item:first-child .btn {
    border-left: 1px solid #DCDFE6;
    border-radius: 4px 0 0 4px;
    box-shadow: none !important;
}
.member-nav-group .member-nav-item:last-child .btn{
    border-radius: 0 4px 4px 0;
}
.member-nav-group .member-nav-item .btn.selected{
    color: #fff;
    border-left: 0 !important;
}

/*登录背景选择*/
.ncap-form-default .theme_bg ul{
        font-size: 0;
}
.ncap-form-default .theme_bg li{
    display: inline-block;
    width: 140px;
    height: 100px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    padding: 1px;
    border: 1px solid #fff;
    margin-right: 6px;
    overflow: hidden;
}
.ncap-form-default .theme_bg .active{
    border: 1px solid #00b240;
}
.ncap-form-default .theme_bg img{
    border-radius: 4px;
    width: 140px;
    height: 100px;
}
/*登录背景选择 end*/
.row-bar{
    width: 100%;
    display: inline-block;
    border-bottom: 1px solid #eee;
}
.row-bar .flexigrid .ftitle{
    margin-bottom: 0;
}

.row-bar .flexigrid .mDiv{
    padding: 0;
}

/*标签选择*/
.flexigrid .tagDiv{
    font-size: 0;
    display: inline-block;
    vertical-align: middle;
    border: solid 1px #eee;
}
.flexigrid .tagDiv .btn {
    height: 26px;
    cursor: pointer;
    background-color: #eee;
    border-right: solid 1px #eee;
    color: #666;
    font-size: 12px;
    vertical-align: top;
    letter-spacing: normal;
    display: inline-block;
    border-radius: 0;
}
/*.flexigrid .tDiv2 {
    font-size: 0;
    padding: 6px 0 6px 6px;
    float: left;
    overflow: hidden;
}
*/

/*新增栏目管理样式*/
.arctype .flexigrid .bDiv .typename{ padding-left: 12px;}
.arctype .flexigrid .bDiv td div{padding: 9px 0px;}


/* 大厨新增金额悬停 */
.hint{color: #555; position:relative;display:inline-block;}

.hint:before, .hint:after{
    position:fixed;
    opacity:0;
    z-index:1000000;
    
    -webkit-transition:0.3s ease;
    -moz-transition:0.3s ease;
    pointer-events:none;
}       
.hint:hover:before, .hint:hover:after{opacity:1;}
.hint:before{
    content:'';
    position:absolute;
    background:transparent;
    border:6px solid transparent;
    position:absolute;
}   
.hint:after{
    content:attr(data-hint);
    background:rgb(30 30 30 / 80%);
    color:white;
    padding:2px 10px;
    font-size:12px;
    white-space:nowrap;
    box-shadow:4px 4px 8px rgba(0, 0, 0, 0.3);
}

/* top */
.hint-top:after{
    position:absolute;
    bottom:100%;
    left:-100%;
    margin:0 0 -6px -10px;
}
.hint-top:hover:after{
    margin-bottom:-2px;
}
/* 大厨新增全局超出隐藏*** */
.wot_z{
    white-space: nowrap!important;
    overflow: hidden!important;
    text-overflow: ellipsis!important;
}

/* 大厨新增模板列表图标样式修正*** */
.flt_u { float: unset!important;margin-bottom: 2px;}
/*TAG 标签 显示框*/
.ncap-form-default .often_tags{
    position: absolute;
    display:block;
    width:calc(100% - 166px);
    top: 50px;
    padding: 10px;
    background: #f8f8f8;
    box-shadow: 0 0 10px #aaa;
    z-index: 2;
    overflow: hidden;
}
.ncap-form-default .often_tags a{
    display: inline-block;
    margin-right: 10px;
    font-size: 13px;
    padding: 0 2px;
    line-height: 1.5;
}
.ncap-form-default .often_tags a.cur{
    background-color:#fdef9f;
    color: #000;
}
/* 评价星星 */
.z_comment-star {display: inline-block; width: 78px;height: 14px;background: url(../images/star.png) no-repeat}
.z_star0 {background-position: -80px 0}
.z_star1 {background-position: -64px 0}
.z_star2 {background-position: -48px 0}
.z_star3 {background-position: -32px 0}
.z_star4 {background-position: -16px 0}
.z_star5 {background-position: 0 0}


.select-show-box{
    margin-top: 30px;
    padding: 20px;
    text-align: center;
}
.select-show-box>a{
    border: 1px solid #eee;
    display: inline-block;
    height: 50px;
    line-height: 50px;
    padding: 0 26px;
    color: #fff;
    font-size:20px;
    margin: 0 10px;
}
.select-show-box .fa{font-size: 25px;}
.select-show-box .fa-mobile{
    margin-top: 6px;
    float: left;
    font-size: 40px !important;
}
.select-show-box .pc{
    background-color: #03a9f4;
    border-color:#03a9f4;
}
.select-show-box .mobile{
    background-color: #4caf50;
    border-color:#4caf50;
}

/*更多功能-导航遮罩*/
.page.on-off_show{
    position: relative;
    
}
.page.on-off_show .on-off_shade {
    display: flex !important;
}
.page .on-off_shade {
    position: absolute;
    height: 100%;
    width: 100%;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.6);
    z-index: 999;
    top: 0;
    display: none;
}
.page.on-off_show .on-off_shadeCon {
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
    background-color: #FFFFFF;
    display: inline-block;
    height: auto;
    text-align: center;
    padding: 16px;
}

/*内容添编辑页 2021-11-12*/

.style2021 .site-location{
    margin: -25px -15px 0;
    height: 26px;
    font-size: 16px;
    padding: 10px 0;
    background-color: #f4f4f4;
}
.style2021 .site-location a{
    color: #3398cc;
}
.style2021 .title{
    font-size: 18px;
    color: #373737;
    line-height: 70px;
    height: 70px;
    width: 100%;
}
.style2021 .bor-b{
    border-bottom: 1px solid #eee;
}
.style2021 .bor-t{
    border-top: 1px solid #eee;
}

.style2021 input[type="text"],.style2021 input[type="password"]{
    line-height: 26px;
    height: 26px;
    font-size: 14px;
}
.style2021 select{
    font-size: 14px;
}
.style2021 .ncap-form-default dl.row:nth-child(even) {
    background-color: #fff;
}
.style2021 .ncap-form-default dl.row {
    border-top: none;
    padding: 14px 0;
}
.style2021 .ncap-form-default dt.tit, 
.style2021 .ncap-form-default dd.opt {
    line-height: 36px;
}

.ncap-form-default dt.tit a.ui_tips {
    background: url(../images/ui_tip2.png) no-repeat center 2px;
    display: inline-block;
    text-indent: -999px;
    width: 12px;
    height: 19px;
    vertical-align: middle;
    margin-left: 3px;
}
.style2021 .input-file-show span.show {
    height: 34px;
}
.style2021 .type-file-box {
    height: 36px;
}
.style2021 .input-file-show span.show i{
    line-height: 34px;
    margin: 0 6px;
}

.style2021 .input-file-show input[type="text"]{
    height: 36px !important;
    line-height: 36px !important;
}
.style2021 .type-file-file {
    height: 34px;
}
.style2021 .type-file-button, .style2021 .type-file-button:focus{
    height: 36px;
}
.style2021 div.tab-pane.pics .delect::after {
    top: -40px;
}
.style2021 .edui-default{
    line-height: 24px;
}
.style2021 .ncap-form-default dt.tit{
    color:#8b8b8b; 
}
.style2021 .ncap-form-default .opt{
    color:#545454; 
}


.style2021 .checkbox-label .checkbox,.style2021 .radio-label .radio {
    display: none;
}

.style2021 .checkbox-label {
    position: relative;
    font-family: sans-serif;
    display:inline-block;
    padding-left:15px;
    user-select: none;
    margin-right: 10px;
}
.style2021 .el-table .checkbox-label{
    padding-left:0;
}

.style2021 .checkbox-label .check-mark {
    width: 15px;
    height: 15px;
    background-color: #ffffff;
    position: absolute;
    left: 0;
    display: inline-block;
    top:10px;
    border-radius: 1px;
    border: 1px solid #e4eaec;
}

.style2021 .checkbox-label.table-checkbox .check-mark {
    top:-6px;
}
.style2021 .checkbox-label.release-checkbox .check-mark{
    top:7px;
}
.style2021 .checkbox-label .checkbox:checked+.check-mark {
    background-color:#3398cc;
    border: 1px solid #3398cc;
    transition: .1s;
}

.style2021 .checkbox-label .checkbox:checked+.check-mark:after {
    content: "";
    position: absolute;
    width: 8px;
    transition: .1s;
    height: 5px;
    background:#3398cc;
    top: 40%;
    left: 50%;
    border-left: 2px solid #fff;
    border-bottom: 2px solid #fff;
    transform: translate(-50%, -50%) rotate(-45deg);
}

.style2021 .radio-label {
    position: relative;
    font-family: sans-serif;
    display:inline-block;
    padding-left:15px;
    user-select: none;
    margin-right: 10px;
    cursor: pointer;
}

.style2021 .radio-label .check-mark {
    width: 16px;
    height: 16px;
    background-color: #ffffff;
    position: absolute;
    left: 0;
    display: inline-block;
    top: 10px;
    border-radius: 50%;
    border: 1px solid #e4eaec;
}

.style2021 .radio-label .radio:checked+.check-mark {
    border: 1px solid #3398cc;
    background-color: #3398cc;
    transition: .1s;
}

.style2021 .radio-label .radio:checked+.check-mark:after {
    content: "";
    position: absolute;
    width: 8px;
    transition: .1s;
    height: 8px;
    background: #ffffff;
    top: 50%;
    left: 50%;
    margin-top: -4px;
    margin-left: -4px;
    border-radius: 50%;
}


.style2021 .select {
    height: 34px;
    line-height: 34px;
    display: inline-block;
    position: relative;
    vertical-align: middle;
    padding: 0;
    overflow: hidden;
    background-color: #fff;
    color: #555;
    border: 1px solid #eee;
    text-shadow: none;
    z-index: 2;
}

.style2021 .select:before {
    content: "";
    position: absolute;
    top: 10px;
    right: 12px;
    cursor: pointer;
    
    border: solid #aba9a9;
    border-width: 0 1px 1px 0;
    display: inline-block;
    padding: 3px;
    transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
}
.style2021 .select select {
    cursor: pointer;
    padding: 4px 4px 8px;
    width: 100%;
    border: none;
    background: transparent;
    background-image: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}


.style2021 .check-radio-con{
    display: inline-block;
    min-width: 500px;
    padding: 20px;
    background-color: #f8f8f8;
}

.style2021 .file-button{
    background-color: #3398cc;
    border:0;
    color: #ffffff;
    padding: 9px 20px;
}
.style2021 .a-link{
    color:#3398cc;
}
.style2021 .a-link.line{
    text-decoration: underline
}
.style2021 a.ncap-btn-green{
    /*border-radius: 0;*/
}
.style2021 .w300{
    width: 300px !important;
}
.style2021 .w200{
    width: 200px !important;
}
.style2021 .w100{
    width: 100px !important;
}
.style2021 .a-btn{
    padding: 8px 15px;
    background-color: #3398cc;
    color: #fff;
}
.style2021 .a-txt{
    color: #999;
}

.right_menu{
    position:fixed;
    right:20px;
    top:200px;
    background-color: #fff;
    z-index: 9999;
}
.right_menu ul{
    border:1px solid #3398cc;
}
.right_menu ul li a {
    display: block;
    font-size: 14px;
    color: #7f7f7f;
    width: 76px;
    height: 40px;
    line-height: 40px;
    text-decoration: none;
    text-align: center;
    border-bottom: 1px solid #42b2eb;
}
.right_menu ul li:last-child a{
    border-bottom:0
}
.right_menu ul li a:hover,
.right_menu ul li a.current {
    color: #fff;
    background: #42b2eb;
}
.right_menu .menu-head{
    position: relative;
    width: 76px;
    height: 59px;
    background: url(../images/right_menu_head.png) no-repeat center center;
}
.close-btn{
    position: absolute;
    color: #3398cc;
    top: 0;
    right: 0;
    cursor: pointer;
}
.right_menu .tit{
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 40px;
    height: 40px;
    background-color: #3398cc;
    color: #fff;
    font-weight: bold;
}
.right_menu .foot{
    width: 100%;
    display: inline-block;
    text-align: center;
    line-height: 32px;
    height: 32px;
    background-color: #3398cc;
    color: #fff;
}

/*来源下拉*/
.origin-hot{
    position: relative;
}
.dl_origin {
    position: unset !important;
}
.origin-hot-list {
    padding: 10px 0;
    width: 386px;
    position: absolute;
    left: 1px;
    top: 35px;
    margin: 0;
    line-height: 32px;
    font-size: 14px;
    border-radius: 2px;
    box-shadow: 0 1px 5px rgba(0,0,0,.2);
    background-color: #fff;
    display: none;
    z-index: 999999;
}
.origin-hot-list>a {
    display: block;
    color: #333;
    text-decoration: none;
    overflow: hidden;
    padding-right: 10px;
}
.origin-hot-list>a:hover {
    background-color: #f3f3f3;
}
.origin-hot-list>a>div {
    float: left;
    height: 32px;
    overflow: hidden;
}
.origin-hot-list>a>div.number {
    text-align: center;
    width: 22px;
    color: #ff8105
}
.origin-hot-list>a>div.hottxt{
    width: 132px;
   overflow: hidden;
   text-overflow:ellipsis;
   white-space: nowrap;
}
.origin-hot-list>a>div.number.c1 {
    color: #ff2c00
}
.origin-hot-list>a>div.number.c2 {
    color: #ff5a00
}
.origin-hot-list>a>div.number.c3 {
    color: #3cbe85
}

/* 编辑器-电脑端手机端切换按钮 */
.pcwap-onoff { font-size: 0; position: relative; overflow: hidden; display: block; float: left; margin-bottom: 15px;margin-right: 15px;}
.pcwap-onoff label { vertical-align: top; display: inline-block; *display: inline;*zoom: 1;
cursor: pointer; }
.pcwap-onoff input[type="radio"] { position: absolute; top: 0; left: -999px; }
.pcwap-onoff .cb-enable{color: #777;font-size: 12px;width: 70px;height: 22px;line-height: 22px;padding: 1px 9px;text-align: center;background-color: #f1f1f1;}
.pcwap-onoff .cb-disable {color: #777;font-size: 12px;width: 70px;height: 22px;line-height: 22px;padding: 1px 9px;text-align: center;background-color: #f1f1f1;}

.pcwap-onoff .cb-disable.selected { color: #FFF;background-color: #5FB878;}
.pcwap-onoff .cb-enable.selected { color: #FFF; background-color: #5FB878;}

/* 新增订单管理-导航手风琴 */
.order-navBox {
    height: 100%;
    overflow-y: auto;
    padding-top: 18px;
}
.order-navBox::-webkit-scrollbar {
  /*滚动条整体样式*/
  width : 6px;  /*高宽分别对应横竖滚动条的尺寸*/
  height: 1px;
  }
.order-navBox::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius   : 10px;
  background-color: #4a4a4b3d;
  background-image: -webkit-linear-gradient(
      45deg,
      transparent 100%,
      transparent
  );
  }
.order-navBox::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background   :unset;
  border-radius: 10px;
  }
.order-navBox ul li a {
    position: relative;
    padding-left: 10px;
}
.order-navBox ul li a i {
    position: absolute;
    top: 50%;
    right: 5px;
    display: inline-block;
    transform-origin: 4px 3px;
    -webkit-transform: translate(0, -50%);
    -moz-transform: translate(0, -50%);
    -ms-transform: translate(0, -50%);
    -o-transform: translate(0, -50%);
    transform: translate(0, -50%);
}
.order-navBox ul li.open a i {
    top: 45%;
    right: 3px;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    transform: rotate(90deg);
}
.order-navBox ul li .submenu {
}
.order-navBox ul li .submenu a {
    min-height: 40px;
    line-height: 40px;
    padding-left: 20px;
    color: #444;
    cursor: pointer;
}
.order-navBox ul li .submenu a:hover {
    background-color: #f5f5f5;
}
.order-navBox ul li .submenu a.current {
    background-color: #f5f5f5;
}



.geduan-xin {
    margin-top: 15px;
    border-bottom: 1px solid #f4f4f4;
}
.flexigrid-xin * {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.flexigrid-xin a {
    color: #555;
}


.header-bar {
    width: 100%;
    border-radius: 2px;
}
.header-bar>.more>.search {
    background-color: #ffffff;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.header-bar>.more>.search .content .ey-form-item {
    margin-right: 0;
    display: inline-block;
    vertical-align: top;
    zoom: 1;
}
.header-bar>.more>.search .content .ey-form-item .users-form-item-label {
    padding-right: 0;
    color: #262b30;
    line-height: 20px;
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    padding: 10px 5px 10px 0;
    box-sizing: border-box;
}
.header-bar>.more>.search .content .ey-form-item .users-form-item-content {
    display: inline-block;
    position: relative;
    line-height: 40px;
    font-size: 14px;
}
.header-bar>.more>.search .content .ey-form-item .users-form-item-content .users-select {
    display: inline-block;
    margin-right: 35px;
    box-sizing: border-box;
    vertical-align: middle;
    color: #515a6e;
    font-size: 14px;
    line-height: normal;
}
.header-bar>.more>.search .content .ey-form-item .users-form-item-content .users-select .users-select-selection select{
    padding-right: 25px;
}
.header-bar>.more>.search .content .ey-form-item .users-form-item-content .users-select .users-select-selection select option{
    line-height: normal;
    clear: both;
    color: #515a6e;
    font-size: 14px!important;
    background: #fff;
    white-space: nowrap;
    list-style: none;
    cursor: pointer;
    transition: background .2s ease-in-out;
}
.header-bar>.more>.search .content .ey-form-item .bt-xin{
    display: inline-block;
    zoom: 1;
    border-radius: 0;
    color: #3398cc;
    height: 29px;
    line-height: 24px;
    padding: 1px 15px 1px 15px;
    margin-right: 10px;
    font-size: 12px;
    background: #fff;
    border: 1px solid #3398cc;
    cursor: pointer;
}
.header-bar>.more>.search .content .ey-form-item .bt-xin:last-child{
    margin-right: 0;
}
.header-bar>.more>.search .content .ey-form-item .bt-xin:hover {
    background-color: #3398cc;
    color: #fff;
}
.header-bar>.more>.search .content .ey-form-item .bt-xin-no{
    display: inline-block;
    zoom: 1;
    border-radius: 0;
    color: #8f8f8f;
    height: 29px;
    line-height: 24px;
    padding: 1px 15px 1px 15px;
    margin-right: 10px;
    font-size: 12px;
    border: 1px solid #8f8f8f;
    cursor: pointer;
}
.header-bar>.more>.search .content .ey-form-item .bt-xin-xia{
    color: #3b639f;
    cursor: pointer;
    padding-right: 0;
    line-height: 20px;
    vertical-align: middle;
    font-size: 14px;
    box-sizing: border-box;
    display: inline-flex;
}
.header-bar>.more>.search .content .ey-form-item .bt-xin-xia i{
    font-size: 20px;
    color: #3b639f;
    margin-right: 2px;
    cursor: pointer;
}
.header-bar>.more>.search .content .navbar-form{
    margin-right: 0;
    display: inline-block;
    vertical-align: top;
    zoom: 1;
}

.users-form-item:after, .users-form-item:before {
    content: "";
    display: table;
}
.flexigrid-xin .sDiv{
    display: inline-block;
    line-height: 37px;
}
.flexigrid-xin .sDiv2 {
    position: relative;
    border: solid 1px #eee;
    display: inline-block;
    box-sizing: border-box;
    vertical-align: middle;
    color: #515a6e;
    font-size: 14px;
    line-height: normal;
}

.flexigrid-xin .sDiv2 .qsbox{
    height: 29px !important;
    padding: 0 5px !important;
}
.flexigrid-xin .mDiv {
    min-height: 26px;
    display: flow-root;
    padding: 0;
}
.flexigrid-xin .mDiv .ftitle {
    height: 40px;
    line-height: 34px;
    margin-bottom: 0;
}
.flexigrid-xin .tableDiv thead {
    background-color: #f7f7f7;
    clear: both;
    position: relative;
    z-index: 1;
    overflow: hidden;
    
}
.flexigrid-xin .tableDiv thead tr {
    border: unset;
    /* border-bottom: 1px solid #e9edef; */
}
.flexigrid-xin .tableDiv thead tr th div{
    padding: 0px 10px;
    color: #262b30;
}

.flexigrid-xin .tableDiv tr th {
    /* font-weight: bold; */
    white-space: nowrap;
    cursor: default;
    overflow: hidden;
    border-right: 2px solid #fff;
    box-sizing: border-box;
}

.flexigrid-xin .tableDiv th div,
.flexigrid-xin .tableDiv td div,
.flexigrid-xin .tableDiv td {
    line-height: 30px;
    white-space: nowrap;
    padding: 3px 10px;
    overflow: hidden;
}

.flexigrid-xin .tableDiv tr td {
    border: 1px solid #e9edef;
    border-right: 1px solid #f4f4f4;
}
.flexigrid-xin .tableDiv tr td:last-child {
    border-right: 1px solid #e9edef;
}


.flexigrid-xin .tableDiv tbody.no-border tr td {
    border: none;
    vertical-align: middle;
    text-align: left;
    font-size: 13px !important;
    white-space: nowrap;
    padding: 0;
    margin: 0;
    overflow: visible;
    border-bottom: 1px solid #f7f7f7;
    font-family: "Segoe UI", "Lucida Grande", Helvetica, Arial, "Microsoft YaHei", FreeSans, Arimo, "Droid Sans","wenquanyi micro hei","Hiragino Sans GB", "Hiragino Sans GB W3", Arial, sans-serif;
}

.flexigrid-xin .tableDiv tbody.no-border td div {
    display: block;
    line-height: 30px;
    text-overflow: ellipsis;
    padding: 14px 10px;
    overflow: hidden;
}
.flexigrid-xin .tableDiv tbody.no-border td div i.zhifu{
    vertical-align: bottom;
    margin-right: 5px;
}
.flexigrid-xin .tableDiv tbody.no-border td div i.e-yuezhifu{
    color: #ff9700;
}
.flexigrid-xin .tableDiv tbody.no-border td div i.e-weixinzhifu{
    color: #1fbe2a;
}
.flexigrid-xin .tableDiv tbody.no-border td div i.e-zhifubaozhifu{
    color: #06b4fd;
}
.flexigrid-xin .tableDiv tbody.no-border td div a.btn.blue{
    color: #3b639f;
}
.flexigrid-xin .tableDiv tr.order-empty {
    height: 15px;
    border: none;
}
.flexigrid-xin .tableDiv tr.order-empty td{
    border: none;
}

.flexigrid-xin .tableDiv .biaotou .sign {
    position: relative;
    padding: 0 4px;
}

.flexigrid-xin .tableDiv .biaotou .sign input {
    position: absolute;
    top: 3px;
    left: 0;
}

.flexigrid-xin .bDiv .sign input {
    margin: 0 !important;
}

.flexigrid-xin .tableDiv .sign-xin {
    position: relative;
    padding: 0 !important;
}

.flexigrid-xin .tableDiv .sign-xin input {
    position: unset;
    top: 3px;
    left: 0;
}
.flexigrid-xin .tableDiv span.zt {
    font-size: 18px;
}
.flexigrid-xin .tableDiv .operation {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    padding: 0 10px;
}
.flexigrid-xin .tableDiv .operation a.bt {
    position: relative;
    padding: 2px 5px;
    font-size: 14px;
    color: #3b639f;
}
.flexigrid-xin .tableDiv .operation span.bt {
    position: relative;
    padding: 2px 5px;
    font-size: 14px;
    color: #555;
}
.flexigrid-xin .tableDiv .operation span.bt .num {
    position: absolute;
    width: 14px;
    height: 14px;
    text-align: center;
    border-radius: 50px;
    background-color: red;
    color: #fff;
    font-size: 12px;
    line-height: 14px;
    right: -8px;
    top: 3px;
}
.flexigrid-xin .biaotou{
    padding: 3px 10px !important;
    background: #f8f8f8;
    color: #939799;
}
.flexigrid-xin .biaotou .copy{
    color: #3b639f;
    cursor: pointer;
}
.flexigrid-xin .biaotou .right {
    padding: 0 20px;
}
.flexigrid-xin .goods-detail {
    display: flex;
    min-width: 280px;
}

.flexigrid-xin .goods-detail>div {
    float: left;
}

.flexigrid-xin .goods-detail .goods-image {
    padding: 8px;
    min-width: 88px;
}
.flexigrid-xin .goods-detail .goods-image img {
    width: 72px;
    height: 72px;
}

.flexigrid-xin .goods-detail .goods-info {
    padding: 8px 0;
}

.flexigrid-xin .goods-detail .goods-info .goods-spec {
    font-size: 12px;
}
.flexigrid-xin .goods-detail .goods-info .goods-spec span{
    padding: 2px 5px;
    margin-right: 10px;
    background: #f5f5f5;
    color: #939799;
}
.flexigrid-xin .goods-detail .goods-info p {
    display: block;
    white-space: normal;
    margin: 0 0 5px 0;
    padding: 0 5px;
    text-align: left;
    line-height: 20px;
}
.flexigrid-xin .goods-detail .goods-info p:last-child {
    margin-bottom: 0;
}
.flexigrid-xin .goods-detail .goods-info p.goods-title {
    max-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    text-align: left !important;
    white-space: normal;
}
.flexigrid-xin .goods-detail .goods-info p.goods-title span{
    display: inline-block;
    margin-right: 5px;
    color: #00c5c5;
    width: 18px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    background-color: #e6fff9;
}
.flexigrid-xin .goods-detail .goods-info p.goods-title span.shi{
    color: #2d8cf0;
    background: #f0faff;
}
.flexigrid-xin .goods-detail .goods-info p.after-sale{
    font-size: 12px;
}
.flexigrid-xin .goods-detail .goods-info p.after-sale span{
    background: #eb6060;
    color: #fff;
    padding: 2px 5px;
    margin-right: 10px;
}
.flexigrid-xin .goods-detail .goods-info p.after-sale a{
    color: #3b639f;
    cursor: pointer;
}

.no_row {
    width: 78px;
    line-height: 56px;
    height: auto !important;
    min-height: 150px;
    text-align: center;
    margin: 0 auto;
}



/* 会员内页-新版 */
.fixed-bar .right .fbutton {
    line-height: 38px;
}

.fixed-bar .right .fbutton .duobt {
    font-size: 13px;
    line-height: 20px;
    color: #555;
    border: 1px solid #ddd;
    background-color: #F7F7F7;
    padding: 3px 9px;
    cursor: pointer;
}

.users-tabs-content * {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

.users-form-con {
    width: 33%;
    display: inline-grid;
}
.users-row {
    display: flex;
    flex-flow: row wrap;
}

.users-form-item {
    margin-bottom: 24px;
    vertical-align: top;
    zoom: 1;
}
.users-form-item:after, .users-form-item:before {
    content: "";
    display: table;
}
.users-form-item:after {
    clear: both;
    visibility: hidden;
    font-size: 0;
    height: 0;
}
.users-form .users-form-item {
    margin-bottom: 30px;
    margin-right: 0;
}
.users-form .users-form-item-label {
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #262b30;
    line-height: 20px;
    padding: 10px 12px 10px 0;
    box-sizing: border-box;
}
.users-form-item-content {
    position: relative;
    line-height: 32px;
    font-size: 14px;
}
.users-poptip {
    display: inline-block;
}
.users-poptip-rel {
    display: inline-block;
    position: relative;
}
.users-tooltip {
    display: inline-block;
}
.users-tooltip-rel {
    display: flex;
    position: relative;
    width: inherit;
}
a.users-btn, .users-btn:not(.users-btn-small) {
    padding: 5px 15px;
    border-radius: 2px;
    font-weight: normal;
    font-size: 14px;
    line-height: 20px;
    height: inherit;
}
a.users-btn-text, .users-btn.users-btn-text {
    padding: 0;
    margin-right: 5px;
    color: #3398cc;
    width: initial;
    border: none;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    background: unset;
    cursor: pointer;
}
.users-col-span-4 {
    display: block;
    flex: 0 0 25%;
    max-width: 25%;
}
.users-col-span-4:nth-child(4n)  {
    padding-right: 0 !important;
}
.users-col-span-6 {
    display: block;
    flex: 0 0 16.66%;
    max-width: 16.66%;
}
.users-col-span-7 {
    display: block;
    width: 33.33% !important;
}
.users-col-span-100 {
    display: block;
    width: 100%;
}

.users-col-span-xxl-6 {
    display: block;
    flex: 0 0 25%;
    max-width: 25%;
}
.users-col-span-xxl-6:nth-child(4n)  {
    padding-right: 0 !important;
}
.users-col {
    position: relative;
    width: 100%;
    max-width: 100%;
    min-height: 1px;
}
.users-tabs-content {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    transform: none!important;
}

.users-tabs-content .users-tabs-tabpane {
    flex-shrink: 0;
    width: 100%;
    transition: opacity .3s;
    opacity: 1;
    outline: 0;
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}
.users-tabs-content .users-tabs-tabpane .vip-detail-base {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}
/* .vip-detail-base>.content {
    padding: 0 40px 50px 40px;
} */
.vip-detail-base>.content .base {
    padding-bottom: 40px;
}
.form-title {
    font-size: 14px;
    line-height: 22px;
    padding: 9px 20px;
}
.form-title.hasMargin {
    position: relative;
    margin-bottom: 30px;
    background-color: #f7f7f7;
    clear: both;
    z-index: 1;
    overflow: hidden;
}
.form-title span {
    border-left: 3px solid #88B7E0;
    margin-right: 8px;
}
.vip-detail-base>.content .base .user-image-r {
    margin-left: 40px;
    margin-right: 20px;
    display: inline-block;
    float: left;
}
.vip-detail-base>.content .base .user-image-r>.user-image-p{
    width: 90px;
    height: 90px;
    border: 4px solid #ffffff;
    border-radius: 100%;
    display: inherit;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -ms-flex-negative: 0;
    flex-shrink: 0;
    overflow: hidden;
}
.vip-detail-base>.content .base .user-image-r>.user-image-p>img {
    width: 84px;
    height: 84px;
    border-radius: 100%;
}
.vip-detail-base>.content .base .user-image-r>.user-image-n {
    margin-top: 15px;
    text-align: center;
    font-size: 14px;
    width: 90px;
}
.vip-detail-base>.content .base .user-image-r>.user-image-n .n-name {
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
}
.vip-detail-base>.content .base .user-image-r>.user-image-h {
    margin-top: 15px;
    text-align: center;
    font-size: 14px;
    color: #3398cc;
}
.vip-detail-base>.content .base .user-image-r>.user-image-h i{
    margin-right: 3px;
}
.vip-detail-base>.content .base-information .user {
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
}

.vip-detail-base>.content .base-information .user>.user-information {
    width: 100%;
}
.vip-detail-base>.content .base-information .user>.user-information .name {
    font-size: 14px;
    font-weight: bold;
    line-height: 20px;
    color: #262b30;
    padding-bottom: 20px;
    padding-left: 0;
}
.vip-detail-base .users-form .users-form-item {
    margin-bottom: 20px;
}
.vip-detail-base>.content .base-information .users-form-item {
    width: 100%;
    height: 32px;
    margin-bottom: 8px;
    position: relative;
    float: left;
    display: inline-flex;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-label {
    padding: 0;
    display: inline-block;
    text-align: right;
    width: 80px;
    height: 32px;
    line-height: 32px;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content {
    width: 70%;
    height: 32px;
    line-height: 32px;
    padding-left: 6px;
    display: inline-block;
    white-space:nowrap;
    overflow:hidden; 
    text-overflow:ellipsis;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .text {
    display: flex;
    text-indent: 5px;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .text .n-name{
    width: auto;
    max-width: 152px;
    font-weight: 600;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .text a{
    cursor: pointer;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .input-txt{
    height: 32px;
    line-height: 32px;
    border: unset;
    border-bottom: 2px solid #eee;
    width: auto;
    padding: 0;
    margin-right: 3px;
    text-indent: 5px;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content input:hover, .vip-detail-base>.content .base-information .users-form-item .users-form-item-content input:focus{
    box-shadow: 0 5px 0px 0px rgba(51,152,204,0.15) !important;
    border-bottom: solid 2px rgba(51,152,204,0.8);
}


.vip-detail-base>.content .base-information .users-form-item .users-form-item-content a.ui_tips {
    background: url(../images/ui_tip.png) no-repeat;
    opacity: 0.5;
    background-size: 100%;
    display: inline-block;
    text-indent: -999px;
    width: 15px;
    height: 21px;
    vertical-align: bottom;
    margin-left: 3px;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content select{
    width: 152px;
    border: 1px solid #eee;
    cursor: pointer;
    margin-bottom: 1px;
    padding: 2px 0px;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content span input{
    border: unset;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content:hover span input{
    border-color: rgba(51,152,204,0.8);
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .onoff {
    margin-top: 5px;
    position: unset;
    float: unset;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .onoff .cb-enable{
    height: unset;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .onoff .cb-disable{
    height: unset;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .input-file-show{
    width: 100%;
    height: 30px;
    line-height: 30px;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .input-file-show .type-file-box{
    width: 100%;
    height: 30px;
    line-height: 30px;
}
.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .input-file-show .type-file-box .type-file-text{
    height: 28px !important;
    line-height: 28px !important;
}

.vip-detail-base>.content .base-information .users-form-item .users-form-item-content .input-file-show .type-file-box .type-file-file{
    box-sizing:unset;
}

.vip-detail-base>.content .base-information .user>.user-information .telephone {
    padding-right: 10px;
}
.vip-detail-base>.content .base-information .user>.user-information .users-btn {
    vertical-align: inherit;
}
.vip-detail-base>.content .base-information .source {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.vip-detail-base>.content .base-information .source .users-tooltip-rel {
    padding-right: 10px;
    line-height: 32px;
    height: 32px;
    vertical-align: middle;
    display: flex;
    word-break: break-all;
}
.vip-detail-base>.content .base-information .source .users-tooltip-rel input{
    line-height: 32px;
    height: 32px;
    margin-right: 3px;
}
.vip-detail-base>.content .base-information .source .users-tooltip-rel img{
    width: 20px;
    height: 20px;
}



.vip-detail-base>.content .asset {
    padding-bottom: 40px;
}
/* .vip-detail-base>.content .asset-information {
    margin-right: -30px;
    padding: 0 20px;
} */
.vip-detail-base>.content .asset-information>.users-col {
    padding-right: 20px;
    padding-bottom: 20px;
}
.vip-detail-base>.content .asset-information>.users-col .asset-information-content {
    padding: 20px;
    border-radius: 2px;
    background: #f7f9fa;
}
.vip-detail-base>.content .asset-information>.users-col .asset-information-content .asset-head{
    display: inline-block;
    width: 100%;
}
.vip-detail-base>.content .asset-information>.users-col .asset-information-content .asset-head .title-l {
    display: inline-block;
    float: left;
    font-size: 16px;
    font-weight: normal;
    line-height: 20px;
    color: #9c9c9c;
}
.vip-detail-base>.content .asset-information>.users-col .asset-information-content .asset-head .title-r {
    display: inline-block;
    float: right;
}
.vip-detail-base>.content .asset-information>.users-col .asset-information-content .asset-head .title-r .users-btn.users-btn-text:last-child{
    margin-right: unset; 
 }
.vip-detail-base>.content .asset-information>.users-col .asset-information-content .asset-con .data {
    font-size: 20px;
    line-height: 24px;
    padding: 10px 0 0 0;
    color: #f97721;
}
.vip-detail-base>.content .asset-information>.users-col .asset-information-content .asset-con .data span{
    margin-right: 15px;
}
.vip-detail-base>.content .asset-information>.users-col .asset-information-content .asset-con .data span:last-child{
    margin-right: unset; 
 }
 .vip-detail-base>.content .asset-information>.users-col .asset-information-content .asset-con .data span em{
     font-size: 16px;
 }
.vip-detail-base>.content .deal {
    padding-bottom: 40px;
}
.vip-detail-base>.content .deal-information {
    margin-bottom: -20px;
}
.vip-detail-base>.content .deal-information>.users-col {
    padding-right: 20px;
    padding-bottom: 20px;
}
.vip-detail-base>.content .deal-information>.users-col>.deal-information-content {
    width: 100%;
    padding: 20px;
    background-color: #f7f9fa;
}
.vip-detail-base>.content .deal-information>.users-col>.deal-information-content>div {
    white-space: nowrap;
}
.vip-detail-base>.content .deal-information>.users-col>.deal-information-content .tit-name {
    color: #9e9e9e;
    font-size: 18px;
}
.vip-detail-base>.content .deal-information>.users-col>.deal-information-content .Statistics_center_left_item_list {
    width: 100%;
    margin-top: 18px;
    justify-content: space-between;
    align-items: center;
    display: flex;
    flex-direction: row;
}
.vip-detail-base>.content .deal-information>.users-col>.deal-information-content .Statistics_center_left_item_list .flex-dir-column {
    display: flex;
    flex-direction: column;
}
.vip-detail-base>.content .deal-information>.users-col>.deal-information-content .Statistics_center_left_item_list .flex-dir-column .column-shu {
    color: #f97721;
    font-size: 20px;
}
.vip-detail-base>.content .deal-information>.users-col>.deal-information-content .Statistics_center_left_item_list .flex-dir-column .column-biao {
    margin-top: 4px;
    color: #6a6a6a;
}
.vip-detail-base .footer-btn {
    padding: 25px 0 10px 0;
    border-top: 1px solid var(--border-color)
}

.vip-detail-base .footer-btn .black-l {
    padding: 4px 33px;
    margin-left: 15px;
    background: #3398cc;
    cursor: pointer;
    color: #fff;
    border: 1px solid #3398cc;
    cursor: pointer;
}
.vip-detail-base .footer-btn .black-r {
    padding: 6px 100px;
    margin-right: 16px;
    color: #f97721;
    border: 1px solid #f97721;
    background: unset;
    cursor: pointer;
}
.users-col-z{
    position: relative;
    margin-left: 150px;
    width: 100%;
    min-height: 1px;
}

.zdy-tit{
    display: block;
    width: 100%;
    margin-top: 15px;
    margin-bottom: 15px;
    border-bottom: solid 1px #eee;
}


/* 会员编辑-弹窗 */
.users-modal-body * {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}
.users-modal-body {
    padding: 16px;
    font-size: 14px;
    line-height: 1.5;
}
.users-modal-body>.content {
    overflow-y: auto;
    position: relative;
}
.users-modal-body>.content .recharge-balance-modal {
    padding: 0;
}
.users-form .users-form-item {
    margin-bottom: 20px;
    margin-right: 0;
}
.users-form .users-form-item-label {
    text-align: right;
    vertical-align: middle;
    float: left;
    font-size: 14px;
    color: #262b30;
    padding: 8px 0 7px 0;
    line-height: 20px;
    box-sizing: border-box;
}
.recharge-balance-modal .balance-data {
    margin-top: 2px;
    color: #ff9900;
    font-size: 18px;
    font-weight: bold;
    line-height: 25px;
    line-height: 32px;
}
.users-radio-group {
    display: inline-block;
    font-size: 14px;
    vertical-align: middle;
}
.users-radio-wrapper {
    font-size: 14px;
    vertical-align: middle;
    display: inline-block;
    position: relative;
    white-space: nowrap;
    margin-right: 8px;
    cursor: pointer;
}
.users-radio-wrapper {
    margin-right: 65px;
    line-height: 20px;
}
.users-form .r-form-item-checkbox .users-radio-wrapper {
    -webkit-transform: translateY(-2px);
    transform: translateY(-2px);
}
.users-radio {
    
}
.users-radio-input {
    position: absolute;
    top: -3px;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    cursor: pointer;
}
.users-radio-input-tit {
    position: absolute;
    top: -6px;
    bottom: 0;
    left: 18px;
    right: 0;
    z-index: 1;
    cursor: pointer;
}
.users-input-wrapper {
    display: inline-block;
    width: 100%;
    position: relative;
    vertical-align: middle;
    line-height: normal;
}
.users-input-group {
    display: inline-block;
    width: 100%;
    border-collapse: separate;
    position: relative;
    font-size: 14px;
    width: 160px !important;
}
.users-input {
    display: table-cell;
    float: left;
    margin-bottom: 0;
    position: relative;
    z-index: 2;
    width: 100%;
    height: 32px !important;
    line-height: 1.5 !important;
    padding: 4px 7px !important;
    font-size: 14px;
    border: 1px solid #dcdee2;
    border-radius: 2px;
    border-color: #e9edef;
    color: #515a6e;
    background-color: #fff;
    background-image: none;
    position: relative;
    cursor: text;
    transition: border .2s ease-in-out,background .2s ease-in-out,box-shadow .2s ease-in-out;
}
.users-input-group-append {
    display: inline-block;
}
.users-input-type-textarea {
    width: 430px;
}
textarea.users-input {
    max-width: 100%;
    height: auto;
    min-height: 80px;
    vertical-align: bottom;
    font-size: 14px;
}
.users-modal-footer {
    border-top: 1px solid #e8eaec;
    padding: 25px 18px 12px 18px;
    text-align: center;
}
.users-modal-footer .default-long {
    padding: 6px 40px;
    margin-right: 16px;
    color: #262b30;
    border: 1px solid #dcdee2;
    background: unset;
    cursor: pointer;
}
.users-modal-footer .primary-long {
    padding: 6px 40px;
    background: -webkit-gradient(linear, left top, right top, from(#4A505C), to(#45525F)), #f90;
    background: linear-gradient(90deg, #3398cc 0%, #3398cc 100%), #f90;
    color: #fff;
    border: 1px solid #3398cc;
    cursor: pointer;
}
.layui-layer-btn .layui-layer-btn0 {
    border-color: #3398cc !important;
    background-color: #3398cc !important;;
    color: #fff !important;
}

/* 新增添加产品评价 */
.ey-pro-goods {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}
.ey-pro-goods .image {
    border: solid 1px #eee;
    overflow: hidden;
    -ms-flex-negative: 0;
    flex-shrink: 0;
}
.ey-pro-goods .content {
     padding-left: 20px;
     font-size: 14px;
     font-weight: normal;
     line-height: 20px;
     overflow: hidden;
     text-overflow: ellipsis;
     display: -webkit-box;
     -webkit-line-clamp: 2;
     -webkit-box-orient: vertical;
}
.ey-start-ment i {
    margin-right: 10px;
    font-size: 20px;
    color: #e0e0e0;
}
.ey-start-ment i.active {
    color: #e4393c;
}

.users-form-item:after, .users-form-item:before {
    content: "";
    display: table;
}
.ment-comment-table {
    padding: 0 20px;
    max-width: 988px;
    background-color: #fff;
    border: 1px solid #e8eaec;
}
.ment-table-wrapper {
    position: relative;
    overflow: hidden;
}
.ment-table {
    width: inherit;
    height: 100%;
    max-width: 100%;
    overflow: hidden;
    color: #515a6e;
    font-size: 14px;
    line-height: 20px;
    background-color: #fff;
    box-sizing: border-box;
}
.ment-table-header {
    overflow: hidden;
}
.ment-table table {
    table-layout: fixed;
}
.ment-table .ment-table-header thead tr th {
    position: relative;
    overflow: hidden;
    height: 100%;
    min-width: 0;
    padding: 1px 0;
    background-color: #ffffff;
    border-bottom: 1px solid #e9edef;
    white-space: nowrap;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    vertical-align: middle;
}
.ment-table .ment-table-header thead tr th .ment-table-cell {
    display: inline-block;
    font-size: 14px;
    font-weight: bold;
    line-height: 20px;
    padding: 22px 15px;
    color: #262b30;
    vertical-align: middle;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    box-sizing: border-box;
}
.ment-table .ment-table-body tr {
    padding: 0 5px;
}
.ment-table td {
    background-color: #fff;
    border-bottom: 1px solid #e8eaec;
    transition: background-color .2s ease-in-out;
}
.ment-table td, .ment-table th {
    min-width: 0;
    box-sizing: border-box;
    text-align: left;
    text-overflow: ellipsis;
    vertical-align: middle;
}
.ment-table .ment-table-body tr td>div {
    text-overflow: ellipsis;
    white-space: normal;
    word-break: normal;
    box-sizing: border-box;
}
.ment-table .ment-table-cell {
    padding: 16px 15px;
}
 .ment-form-item {
    margin-bottom: 0;
    margin-right: 0;
    vertical-align: top;
    zoom: 1;
}
.ment-form-item .ment-form-item-content {
    margin-left: 0!important;
    position: relative;
    line-height: 32px;
    font-size: 14px;
}
.ment-form-item .ment-form-item-content .ment-input-wrapper {
    display: inline-block;
    width: 600px;
    position: relative;
    vertical-align: middle;
    line-height: normal;
}
.show-img .slide-image {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.show-img ul {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-bottom: 0;
}
.show-img ul>.img-li {
    width: 40px;
    height: 40px;
    position: relative;
    line-height: 40px;
    margin-right: 10px;
    margin-bottom: 10px;
    border: 1px solid #e9edef;
    background-color: #f4f6f8;
    border-radius: 2px;
    line-height: 100px;
    text-align: center;
}
.show-img ul>.img-li>img {
    display: block;
    width: 100%;
    height: 100%;
}
.show-img ul>.img-li>.e-guanbi {
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 16px;
    line-height: 1;
    cursor: pointer;
    color: #b8b9bd;
    display: none;
}
.show-img ul>.img-li:hover>.e-guanbi {
    display: block;
    color: #b8b9bd;
}
.show-img ul>.img-li .single-replace {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    cursor: pointer;
    background-color: rgba(0,0,0,0.7);
    text-align: center;
    line-height: 20px;
    color: #ffffff;
    font-size: 12px;
    display: none;
}
.show-img ul>.img-li:hover>.single-replace {
    display: block;
}
.show-img .slide-image .slide-image-add {
    width: 40px;
    height: 40px;
    background-color: #fff;
    border: 1px dashed #e9edef;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    cursor: pointer;
    border-radius: 2px;
    margin-bottom: 0;
}
.show-img .slide-image .slide-image-add>.text-icon {
    text-align: center;
    line-height: normal;
}
.show-img .slide-image .slide-image-add .text-icon .icon {
    display: block;
    padding-bottom: 0;
    font-size: 16px;
    color: #939799;
}
.show-img .slide-image .slide-image-add .text-icon>span {
    display: none;
}
.ment-btn.ment-btn-text {
    padding: 0;
    color: #2d8cf0;
    width: initial;
    height: inherit;
    border: none;
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    cursor: pointer;
}
.ment-comment-table .add-con{
    text-align: center;
    width: 100%;
    padding-top: 16px;
    padding-bottom: 16px;
    height: inherit;
    
}
.fieldext_upload{
    display: inline-block;
    margin-right: 10px;
    float: left;
}
.fieldext_upload .images_upload_item{
    position: relative;
    width: 100px;
    height: 100px;
    display: block;
    overflow: hidden;
    border: 1px solid #eee;
}
.fieldext_upload .images_upload_item .delete {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 2;
    width: 0px;
    height: 0px;
    border-top: 30px solid rgba(0,0,0,0.5);
    border-left: 30px solid transparent;
}
.fieldext_upload .images_upload_item .delete::after {
    position: relative;
    content: "\d7";
    font-size: 20px;
    color: white;
    top: -33px;
    right: 15px;
}

/* 帮助与设置 */
.help-form-default {
    overflow: hidden;
}
.help-form-default .help-title{
    font-weight: 700;
    font-size: 14px;
    margin: 15px 0 5px 0;
}
.help-form-default .help-title i{
    margin-right: 2px;
    font-weight: 400;
}
.help-form-default dl.help-row {
    font-size: 0;
    color: #777;
    *word-spacing: -1px /*IE6、7*/;
    padding: 5px 0;
    margin-top: -1px;
    position: relative;
    z-index: 1;
}

.help-form-default dl.help-row:first-child {
}

.help-form-default dl.help-row:nth-child(even) {
}

.help-form-default dt.help-tit,
.help-form-default dd.help-opt {
    font-size: 12px;
    line-height: 24px;
    vertical-align: top;
    letter-spacing: normal;
    display: inline-block;
    *display: inline /*IE7*/;
    *zoom: 1 /*IE7*/;
    font-size: 14px;
}

.help-form-default dd.help-opt input[type=checkbox],
.help-form-default dd.help-opt input[type=radio] {
    margin-right: 3px;
}

.help-form-default dt.help-tit {
    text-align: left;
    width: 100px;
}

.help-form-default dd.help-opt {
    text-align: left;
    color: #666;
}
.help-form-default dd.help-opt em{
    border-bottom: 1px dashed #3b639f;
}
.help-form-default dd.help-opt a em{
    color: #777;
}
.help-form-default dd.help-opt a em:hover{
    color: #3b639f;
}
.help-form-default dd.help-opt span{
    text-align: left;
    color: #999;
}


.help-form-default dd.help-opt .opt-moreOper {
    position: relative;
    overflow: hidden;
    margin-top: 10px;
    display: block;
    height: 28px;
}

.help-form-default dd.help-opt .opt-moreOper p {
    position: absolute;
    z-index: 999;
}

.help-form-default dd.help-opt .layui-btn {
    height: 30px;
    line-height: 30px;
    background-color: #4fc0e8;
    border-radius: 0px;
}

.help-form-default dd.help-opt .express-tag {
    margin-top: 10px;
}

.help-form-default dd.help-opt .express-tag>span {
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
    color: #636669;
    padding: 1px 7px;
    border: 1px solid #e9edef;
    border-radius: 2px;
    margin-right: 10px;
    cursor: pointer;
    display: inline-block;
}

.help-form-default dd.help-opt .express-tag>span.select-express {
    border: 1px solid #F60 !important;
    color: #F60 !important;
}

.help-form-default dt.help-tit em {
    font: bold 14px/20px tahoma, verdana;
    color: #F60;
    vertical-align: middle;
    display: inline-block;
    margin-right: 5px;
    margin-left: -14px;
}

/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
.help-page::-webkit-scrollbar{
    width: 4px;
    height: 6px;
    background-color: #F5F5F5;
}

/*定义滚动条轨道 内阴影+圆角*/
.help-page::-webkit-scrollbar-track {
    box-shadow: inset 0 0 6px rgb(113 113 113 / 30%);
    -webkit-box-shadow: inset 0 0 6px rgb(113 113 113 / 30%);
    border-radius: 10px;
    background-color: #ffffff;
}

/*定义滑块 内阴影+圆角*/
.help-page::-webkit-scrollbar-thumb{
    border-radius: 10px;
    box-shadow: inset 0 0 6px rgba(0, 0, 0, .1);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .1);
    background-color: #e2e2e2;
}
.help-page .page{
    padding: 0 20px 0 20px;
}
.help-page .f_jxw{
    position: fixed;
    bottom: 36px;
    right: 0;
    width: 108px;
    height: 108px;
    background: url(../images/jxw.png) no-repeat;
    background-position: bottom right;
    opacity: 0.2;
}