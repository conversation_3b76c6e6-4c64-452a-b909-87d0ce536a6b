{include file="header.htm" /}
<body style="background-color: rgb(255, 255, 255); overflow: auto; cursor: default; -moz-user-select: inherit;">
<div id="append_parent"></div>
<div id="ajaxwaitid"></div>
<div class="page">
    {include file="bar.htm" /}
    <div class="flexigrid">
        <div class="mDiv">
            <div class="ftitle">
                <h3>敏感词列表</h3>
                <h5>(共{$pageObj->totalRows}条记录)</h5>
            </div>
            <div title="刷新数据" class="pReload"><i class="fa fa-refresh"></i></div>
            <form class="navbar-form form-inline" action="{:weapp_url('Wordfilter/Wordfilter/index')}" method="get" onSubmit="layer_loading('正在处理');">
                {$searchform.hidden|default=''}
                <div class="sDiv">
                    <div class="sDiv2">
                        <input type="text" size="30" name="keywords" class="qsbox" placeholder="搜索名称...">
                        <input type="submit" class="btn" value="搜索">
                        <i class="iconfont e-sousuo"></i>
                    </div>
                    <!-- <div class="sDiv2">
                        <input type="button" class="btn" value="重置" onClick="window.location.href='{:weapp_url('Wordfilter/Wordfilter/index')}';">
                    </div> -->
                </div>
            </form>
        </div>
        <div class="hDiv">
            <div class="hDivBox">
                <table cellspacing="0" cellpadding="0" style="width: 100%">
                    <thead>
                    <tr>
                        <th class="sign w40" axis="col0">
                            <div class="tc">选择</div>
                        </th>
                        <th abbr="article_title" axis="col3">
                            <div class="text-l10">敏感词</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w80">
                            <div class="tc">启用</div>
                        </th>
                        <th abbr="article_time" axis="col6" class="w130">
                            <div class="tc">更新时间</div>
                        </th>
                        <th axis="col1" class="w60">
                            <div class="tc">操作</div>
                        </th>
                    </tr>
                    </thead>
                </table>
            </div>
        </div>
        <div class="bDiv" style="height: auto;">
            <div id="flexigrid" cellpadding="0" cellspacing="0" border="0">
                <table style="width: 100%">
                    <tbody>
                    {empty name="list"}
                        <tr>
                            <td class="no-data" align="center" axis="col0" colspan="50">
                                <i class="fa fa-exclamation-circle"></i>没有符合条件的记录
                            </td>
                        </tr>
                    {else/}
                        {volist name="list" id="vo"}
                        <tr>
                            <td class="sign">
                                <div class="w40 tc"><input type="checkbox" name="ids[]" value="{$vo.id}"></div>
                            </td>
                            <td style="width: 100%">
                                <div class="text-l10" style="">
                                    <input type="text" name="title" value="{$vo.title}" onChange="changeTableVal('weapp_wordfilter','id','{$vo.id}','title',this);" style="width: 300px;">
                                </div>
                            </td>
                            <td class="">
                                <div class="w80 tc">
                                    {eq name="$vo['status']" value='1'}
                                        <span class="yes" onClick="changeTableVal('weapp_wordfilter','id','{$vo.id}','status',this);"><i class="fa fa-check-circle"></i>是</span>
                                    {else /}
                                        <span class="no" onClick="changeTableVal('weapp_wordfilter','id','{$vo.id}','status',this);"><i class="fa fa-ban"></i>否</span>
                                    {/eq}
                                </div>
                            </td>
                            <td class="">
                                <div class="w130 tc">
                                    {$vo.update_time|MyDate='Y-m-d H:i:s',###}
                                </div>
                            </td>
                            <td>
                                <div class="w60 tc">
                                    <a class="btn red"  href="javascript:void(0)" data-url="{:weapp_url('Wordfilter/Wordfilter/del')}" data-id="{$vo.id}" onClick="delfun(this);"><i class="fa fa-trash-o"></i>删除</a>
                                </div>
                            </td>
                        </tr>
                        {/volist}
                    {/empty}
                    </tbody>
                </table>
            </div>
            <div class="iDiv" style="display: none;"></div>
        </div>
        <div class="tDiv">
            <div class="tDiv2">
                <div class="fbutton checkboxall">
                    <input type="checkbox" onClick="javascript:$('input[name*=ids]').prop('checked',this.checked);">
                </div>
                <div class="fbutton">
                    <a onClick="batch_del(this, 'ids');" data-url="{:weapp_url('Wordfilter/Wordfilter/del')}">
                        <div class="add" title="批量删除">
                            <span><i class="fa fa-close"></i>批量删除</span>
                        </div>
                    </a>
                </div>
                <div class="fbutton">
                    <a href="{:weapp_url('Wordfilter/Wordfilter/add')}">
                        <div class="add" title="批量新增">
                            <span class="red"><i class="fa fa-plus"></i>批量新增</span>
                        </div>
                    </a>
                </div>
            </div>
            <div style="clear:both"></div>
        </div>
        <!--分页位置-->
        {$pageStr}
    </div>
</div>

<script>
    $(document).ready(function(){
        // 表格行点击选中切换
        $('#flexigrid > table>tbody >tr').click(function(){
            $(this).toggleClass('trSelected');
        });

        // 点击刷新数据
        $('.fa-refresh').click(function(){
            location.href = location.href;
        });
    });
</script>
{include file="footer.htm" /}
